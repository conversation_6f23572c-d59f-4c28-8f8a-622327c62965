#!/usr/bin/env node

/**
 * Wallet Migration Test Script
 * This script verifies that the Credit page has been successfully migrated to Wallet
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Wallet Migration Verification Test');
console.log('=====================================\n');

let allTestsPassed = true;

// Test 1: Check if Wallet.tsx exists and Credit.tsx is removed
function testFileRename() {
    console.log('1. Testing File Rename...');
    
    const walletPath = path.join(__dirname, 'frontend/src/pages/dashboard/Wallet.tsx');
    const creditPath = path.join(__dirname, 'frontend/src/pages/dashboard/Credit.tsx');
    
    const walletExists = fs.existsSync(walletPath);
    const creditExists = fs.existsSync(creditPath);
    
    console.log(`   ✅ Wallet.tsx exists: ${walletExists}`);
    console.log(`   ✅ Credit.tsx removed: ${!creditExists}`);
    
    if (walletExists && !creditExists) {
        console.log('   🎉 File rename: PASS\n');
        return true;
    } else {
        console.log('   ❌ File rename: FAIL\n');
        return false;
    }
}

// Test 2: Check Wallet page content
function testWalletPageContent() {
    console.log('2. Testing Wallet Page Content...');
    
    try {
        const walletPath = path.join(__dirname, 'frontend/src/pages/dashboard/Wallet.tsx');
        const content = fs.readFileSync(walletPath, 'utf8');
        
        const hasWalletTitle = content.includes('Wallet Management');
        const hasWalletDescription = content.includes('Manage your wallet balance, top up your wallet');
        const hasTopUpTab = content.includes('Top Up Wallet');
        const hasWalletComponent = content.includes('const Wallet: React.FC');
        const hasWalletExport = content.includes('export default Wallet');
        
        console.log(`   ✅ Wallet title: ${hasWalletTitle}`);
        console.log(`   ✅ Wallet description: ${hasWalletDescription}`);
        console.log(`   ✅ Top Up tab: ${hasTopUpTab}`);
        console.log(`   ✅ Wallet component: ${hasWalletComponent}`);
        console.log(`   ✅ Wallet export: ${hasWalletExport}`);
        
        if (hasWalletTitle && hasWalletDescription && hasTopUpTab && hasWalletComponent && hasWalletExport) {
            console.log('   🎉 Wallet page content: PASS\n');
            return true;
        } else {
            console.log('   ❌ Wallet page content: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error reading Wallet.tsx: ${error.message}\n`);
        return false;
    }
}

// Test 3: Check routing configuration
function testRouting() {
    console.log('3. Testing Routing Configuration...');
    
    try {
        const appPath = path.join(__dirname, 'frontend/src/App.tsx');
        const content = fs.readFileSync(appPath, 'utf8');
        
        const hasWalletImport = content.includes("import Wallet from './pages/dashboard/Wallet'");
        const hasWalletRoute = content.includes('path="/dashboard/wallet"');
        const hasWalletComponent = content.includes('<Wallet />');
        const hasRedirectRoute = content.includes('path="/dashboard/credit"');
        const hasNavigateRedirect = content.includes('Navigate to="/dashboard/wallet"');
        
        console.log(`   ✅ Wallet import: ${hasWalletImport}`);
        console.log(`   ✅ Wallet route: ${hasWalletRoute}`);
        console.log(`   ✅ Wallet component: ${hasWalletComponent}`);
        console.log(`   ✅ Redirect route: ${hasRedirectRoute}`);
        console.log(`   ✅ Navigate redirect: ${hasNavigateRedirect}`);
        
        if (hasWalletImport && hasWalletRoute && hasWalletComponent && hasRedirectRoute && hasNavigateRedirect) {
            console.log('   🎉 Routing configuration: PASS\n');
            return true;
        } else {
            console.log('   ❌ Routing configuration: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error reading App.tsx: ${error.message}\n`);
        return false;
    }
}

// Test 4: Check navigation menu
function testNavigation() {
    console.log('4. Testing Navigation Menu...');
    
    try {
        const layoutPath = path.join(__dirname, 'frontend/src/components/dashboard/DashboardLayout.tsx');
        const content = fs.readFileSync(layoutPath, 'utf8');
        
        const hasWalletMenuItem = content.includes("text: 'Wallet'");
        const hasWalletPath = content.includes("path: '/dashboard/wallet'");
        const noOldCreditItem = !content.includes("text: 'Credit'");
        const noOldCreditPath = !content.includes("path: '/dashboard/credit'");
        
        console.log(`   ✅ Wallet menu item: ${hasWalletMenuItem}`);
        console.log(`   ✅ Wallet path: ${hasWalletPath}`);
        console.log(`   ✅ No old Credit item: ${noOldCreditItem}`);
        console.log(`   ✅ No old Credit path: ${noOldCreditPath}`);
        
        if (hasWalletMenuItem && hasWalletPath && noOldCreditItem && noOldCreditPath) {
            console.log('   🎉 Navigation menu: PASS\n');
            return true;
        } else {
            console.log('   ❌ Navigation menu: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error reading DashboardLayout.tsx: ${error.message}\n`);
        return false;
    }
}

// Test 5: Check CreditPackages component updates
function testCreditPackagesUpdates() {
    console.log('5. Testing CreditPackages Component Updates...');
    
    try {
        const packagesPath = path.join(__dirname, 'frontend/src/components/credit/CreditPackages.tsx');
        const content = fs.readFileSync(packagesPath, 'utf8');
        
        const hasWalletTopUpTitle = content.includes('Wallet Top-Up Packages');
        const hasTopUpDescription = content.includes('Choose a top-up package');
        const hasWalletRedirectUrl = content.includes('/dashboard/wallet');
        const noOldCreditUrl = !content.includes('/dashboard/credit');
        
        console.log(`   ✅ Wallet Top-Up title: ${hasWalletTopUpTitle}`);
        console.log(`   ✅ Top-up description: ${hasTopUpDescription}`);
        console.log(`   ✅ Wallet redirect URL: ${hasWalletRedirectUrl}`);
        console.log(`   ✅ No old credit URL: ${noOldCreditUrl}`);
        
        if (hasWalletTopUpTitle && hasTopUpDescription && hasWalletRedirectUrl && noOldCreditUrl) {
            console.log('   🎉 CreditPackages updates: PASS\n');
            return true;
        } else {
            console.log('   ❌ CreditPackages updates: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error reading CreditPackages.tsx: ${error.message}\n`);
        return false;
    }
}

// Run all tests
const test1 = testFileRename();
const test2 = testWalletPageContent();
const test3 = testRouting();
const test4 = testNavigation();
const test5 = testCreditPackagesUpdates();

allTestsPassed = test1 && test2 && test3 && test4 && test5;

// Summary
console.log('📊 Test Summary');
console.log('================');
console.log(`Total Tests: 5`);
console.log(`Passed: ${[test1, test2, test3, test4, test5].filter(Boolean).length}`);
console.log(`Failed: ${[test1, test2, test3, test4, test5].filter(t => !t).length}`);
console.log(`Success Rate: ${Math.round(([test1, test2, test3, test4, test5].filter(Boolean).length / 5) * 100)}%`);

if (allTestsPassed) {
    console.log('\n🎉 All tests passed! Wallet migration completed successfully.');
    console.log('\n✅ Summary of Changes:');
    console.log('   - Route changed from /dashboard/credit to /dashboard/wallet');
    console.log('   - Page title changed to "Wallet Management"');
    console.log('   - Tab label changed to "Top Up Wallet"');
    console.log('   - Navigation menu updated to "Wallet"');
    console.log('   - Redirect added for backward compatibility');
    console.log('   - All existing functionality preserved');
} else {
    console.log('\n❌ Some tests failed. Please check the implementation.');
}
