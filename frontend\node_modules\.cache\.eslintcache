[{"C:\\laragon\\www\\frontend\\src\\index.tsx": "1", "C:\\laragon\\www\\frontend\\src\\reportWebVitals.ts": "2", "C:\\laragon\\www\\frontend\\src\\App.tsx": "3", "C:\\laragon\\www\\frontend\\src\\pages\\Home.tsx": "4", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Login.tsx": "5", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Register.tsx": "6", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ForgotPassword.tsx": "7", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ResetPassword.tsx": "8", "C:\\laragon\\www\\frontend\\src\\contexts\\AuthContext.tsx": "9", "C:\\laragon\\www\\frontend\\src\\pages\\user\\Profile.tsx": "10", "C:\\laragon\\www\\frontend\\src\\pages\\user\\EditProfile.tsx": "11", "C:\\laragon\\www\\frontend\\src\\components\\layout\\Navbar.tsx": "12", "C:\\laragon\\www\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "13", "C:\\laragon\\www\\frontend\\src\\pages\\cms\\PageView.tsx": "14", "C:\\laragon\\www\\frontend\\src\\components\\auth\\EmailVerificationNotice.tsx": "15", "C:\\laragon\\www\\frontend\\src\\services\\cmsService.ts": "16", "C:\\laragon\\www\\frontend\\src\\services\\authService.ts": "17", "C:\\laragon\\www\\frontend\\src\\services\\api.ts": "18", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Settings.tsx": "19", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Dashboard.tsx": "20", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Users.tsx": "21", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardRoute.tsx": "22", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardLayout.tsx": "23", "C:\\laragon\\www\\frontend\\src\\theme\\materialDashboardTheme.ts": "24", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\ChartExample.tsx": "25", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DataTable.tsx": "26", "C:\\laragon\\www\\frontend\\src\\components\\credit\\CreditPackages.tsx": "27", "C:\\laragon\\www\\frontend\\src\\components\\credit\\TransactionHistory.tsx": "28", "C:\\laragon\\www\\frontend\\src\\components\\credit\\CreditBalance.tsx": "29", "C:\\laragon\\www\\frontend\\src\\services\\creditService.ts": "30", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Order.tsx": "31", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Orders.tsx": "32", "C:\\laragon\\www\\frontend\\src\\services\\printingService.ts": "33", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileUpload.tsx": "34", "C:\\laragon\\www\\frontend\\src\\components\\common\\ErrorBoundary.tsx": "35", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Wallet.tsx": "36", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTopUp.tsx": "37", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTransactionHistory.tsx": "38", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletBalance.tsx": "39", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\OverdraftPrevention.tsx": "40", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletQuickActions.tsx": "41"}, {"size": 554, "mtime": 1752503120783, "results": "42", "hashOfConfig": "43"}, {"size": 425, "mtime": 1752503120799, "results": "44", "hashOfConfig": "43"}, {"size": 4222, "mtime": 1752840768018, "results": "45", "hashOfConfig": "43"}, {"size": 7912, "mtime": 1752503120830, "results": "46", "hashOfConfig": "43"}, {"size": 4298, "mtime": 1752503120814, "results": "47", "hashOfConfig": "43"}, {"size": 7734, "mtime": 1752504934229, "results": "48", "hashOfConfig": "43"}, {"size": 3447, "mtime": 1752503120814, "results": "49", "hashOfConfig": "43"}, {"size": 5805, "mtime": 1752503120814, "results": "50", "hashOfConfig": "43"}, {"size": 4114, "mtime": 1752503120767, "results": "51", "hashOfConfig": "43"}, {"size": 4077, "mtime": 1752504117348, "results": "52", "hashOfConfig": "43"}, {"size": 11533, "mtime": 1752504965088, "results": "53", "hashOfConfig": "43"}, {"size": 2843, "mtime": 1752535330345, "results": "54", "hashOfConfig": "43"}, {"size": 1355, "mtime": 1752503120861, "results": "55", "hashOfConfig": "43"}, {"size": 4072, "mtime": 1752503120830, "results": "56", "hashOfConfig": "43"}, {"size": 3494, "mtime": 1752503120861, "results": "57", "hashOfConfig": "43"}, {"size": 2009, "mtime": 1752504140096, "results": "58", "hashOfConfig": "43"}, {"size": 3988, "mtime": 1752504129163, "results": "59", "hashOfConfig": "43"}, {"size": 1850, "mtime": 1752619528779, "results": "60", "hashOfConfig": "43"}, {"size": 5100, "mtime": 1752536025085, "results": "61", "hashOfConfig": "43"}, {"size": 3040, "mtime": 1752536704783, "results": "62", "hashOfConfig": "43"}, {"size": 2464, "mtime": 1752536620414, "results": "63", "hashOfConfig": "43"}, {"size": 708, "mtime": 1752534344051, "results": "64", "hashOfConfig": "43"}, {"size": 5570, "mtime": 1752840607407, "results": "65", "hashOfConfig": "43"}, {"size": 2924, "mtime": 1752534243344, "results": "66", "hashOfConfig": "43"}, {"size": 4729, "mtime": 1752536669944, "results": "67", "hashOfConfig": "43"}, {"size": 6604, "mtime": 1752536575892, "results": "68", "hashOfConfig": "43"}, {"size": 9777, "mtime": 1752845926910, "results": "69", "hashOfConfig": "43"}, {"size": 9031, "mtime": 1752584266287, "results": "70", "hashOfConfig": "43"}, {"size": 6155, "mtime": 1752584379676, "results": "71", "hashOfConfig": "43"}, {"size": 5582, "mtime": 1752847027187, "results": "72", "hashOfConfig": "43"}, {"size": 28953, "mtime": 1752707482487, "results": "73", "hashOfConfig": "43"}, {"size": 27549, "mtime": 1752710086718, "results": "74", "hashOfConfig": "43"}, {"size": 10226, "mtime": 1752621266300, "results": "75", "hashOfConfig": "43"}, {"size": 17358, "mtime": 1752708893705, "results": "76", "hashOfConfig": "43"}, {"size": 1430, "mtime": 1752673572781, "results": "77", "hashOfConfig": "43"}, {"size": 7995, "mtime": 1752847459160, "results": "78", "hashOfConfig": "43"}, {"size": 11641, "mtime": 1752847731148, "results": "79", "hashOfConfig": "43"}, {"size": 14608, "mtime": 1752848061488, "results": "80", "hashOfConfig": "43"}, {"size": 8564, "mtime": 1752847710399, "results": "81", "hashOfConfig": "43"}, {"size": 6560, "mtime": 1752847232952, "results": "82", "hashOfConfig": "43"}, {"size": 6774, "mtime": 1752847753245, "results": "83", "hashOfConfig": "43"}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tace3p", {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\frontend\\src\\index.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\laragon\\www\\frontend\\src\\App.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\Home.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Login.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Register.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ForgotPassword.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ResetPassword.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\user\\Profile.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\user\\EditProfile.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\layout\\Navbar.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\cms\\PageView.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\auth\\EmailVerificationNotice.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\cmsService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\services\\authService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\services\\api.ts", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Settings.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Dashboard.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Users.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardRoute.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardLayout.tsx", ["207", "208"], [], "C:\\laragon\\www\\frontend\\src\\theme\\materialDashboardTheme.ts", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\ChartExample.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DataTable.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\credit\\CreditPackages.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\credit\\TransactionHistory.tsx", ["209"], [], "C:\\laragon\\www\\frontend\\src\\components\\credit\\CreditBalance.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\creditService.ts", ["210", "211"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Order.tsx", ["212", "213", "214", "215", "216", "217"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Orders.tsx", ["218", "219", "220"], [], "C:\\laragon\\www\\frontend\\src\\services\\printingService.ts", ["221"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileUpload.tsx", ["222", "223"], [], "C:\\laragon\\www\\frontend\\src\\components\\common\\ErrorBoundary.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Wallet.tsx", ["224"], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTopUp.tsx", ["225", "226"], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTransactionHistory.tsx", ["227", "228"], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletBalance.tsx", ["229"], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\OverdraftPrevention.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletQuickActions.tsx", ["230", "231"], [], {"ruleId": "232", "severity": 1, "message": "233", "line": 26, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 26, "endColumn": 14}, {"ruleId": "232", "severity": 1, "message": "236", "line": 42, "column": 9, "nodeType": "234", "messageId": "235", "endLine": 42, "endColumn": 17}, {"ruleId": "232", "severity": 1, "message": "237", "line": 41, "column": 10, "nodeType": "234", "messageId": "235", "endLine": 41, "endColumn": 20}, {"ruleId": "232", "severity": 1, "message": "238", "line": 1, "column": 15, "nodeType": "234", "messageId": "235", "endLine": 1, "endColumn": 24}, {"ruleId": "239", "severity": 1, "message": "240", "line": 223, "column": 1, "nodeType": "241", "endLine": 223, "endColumn": 36}, {"ruleId": "232", "severity": 1, "message": "242", "line": 18, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 18, "endColumn": 14}, {"ruleId": "232", "severity": 1, "message": "243", "line": 19, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 19, "endColumn": 13}, {"ruleId": "232", "severity": 1, "message": "244", "line": 20, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 20, "endColumn": 9}, {"ruleId": "232", "severity": 1, "message": "245", "line": 21, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 21, "endColumn": 11}, {"ruleId": "232", "severity": 1, "message": "246", "line": 37, "column": 10, "nodeType": "234", "messageId": "235", "endLine": 37, "endColumn": 20}, {"ruleId": "247", "severity": 1, "message": "248", "line": 135, "column": 6, "nodeType": "249", "endLine": 135, "endColumn": 50, "suggestions": "250"}, {"ruleId": "232", "severity": 1, "message": "251", "line": 27, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 27, "endColumn": 10}, {"ruleId": "232", "severity": 1, "message": "252", "line": 41, "column": 17, "nodeType": "234", "messageId": "235", "endLine": 41, "endColumn": 27}, {"ruleId": "247", "severity": 1, "message": "253", "line": 65, "column": 6, "nodeType": "249", "endLine": 65, "endColumn": 12, "suggestions": "254"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 335, "column": 1, "nodeType": "241", "endLine": 335, "endColumn": 38}, {"ruleId": "247", "severity": 1, "message": "255", "line": 76, "column": 6, "nodeType": "249", "endLine": 76, "endColumn": 15, "suggestions": "256"}, {"ruleId": "247", "severity": 1, "message": "257", "line": 188, "column": 6, "nodeType": "249", "endLine": 188, "endColumn": 40, "suggestions": "258"}, {"ruleId": "232", "severity": 1, "message": "259", "line": 73, "column": 9, "nodeType": "234", "messageId": "235", "endLine": 73, "endColumn": 17}, {"ruleId": "232", "severity": 1, "message": "260", "line": 8, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 8, "endColumn": 7}, {"ruleId": "232", "severity": 1, "message": "236", "line": 58, "column": 9, "nodeType": "234", "messageId": "235", "endLine": 58, "endColumn": 17}, {"ruleId": "232", "severity": 1, "message": "251", "line": 29, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 29, "endColumn": 10}, {"ruleId": "232", "severity": 1, "message": "261", "line": 38, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 38, "endColumn": 13}, {"ruleId": "232", "severity": 1, "message": "260", "line": 12, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 12, "endColumn": 7}, {"ruleId": "232", "severity": 1, "message": "260", "line": 8, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 8, "endColumn": 7}, {"ruleId": "232", "severity": 1, "message": "236", "line": 48, "column": 9, "nodeType": "234", "messageId": "235", "endLine": 48, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'ChevronLeft' is defined but never used.", "Identifier", "unusedVar", "'isMobile' is assigned a value but never used.", "'totalPages' is assigned a value but never used.", "'endpoints' is defined but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'orderItems' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'calculatePrice'. Either include it or remove the dependency array.", "ArrayExpression", ["262"], "'Divider' is defined but never used.", "'FilterIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadOrders'. Either include it or remove the dependency array.", ["263"], "React Hook useEffect has missing dependencies: 'loadSettings' and 'loadUploadedFiles'. Either include them or remove the dependency array.", ["264"], "React Hook useCallback has missing dependencies: 'onError', 'uploadFiles', and 'validateFiles'. Either include them or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["265"], "'isTablet' is assigned a value but never used.", "'Grid' is defined but never used.", "'FilterList' is defined but never used.", {"desc": "266", "fix": "267"}, {"desc": "268", "fix": "269"}, {"desc": "270", "fix": "271"}, {"desc": "272", "fix": "273"}, "Update the dependencies array to be: [selectedProduct, quantity, selectedOptions, calculatePrice]", {"range": "274", "text": "275"}, "Update the dependencies array to be: [loadOrders, page]", {"range": "276", "text": "277"}, "Update the dependencies array to be: [loadSettings, loadUploadedFiles, orderId]", {"range": "278", "text": "279"}, "Update the dependencies array to be: [onError, orderId, uploadFiles, validateFiles]", {"range": "280", "text": "281"}, [4644, 4688], "[selectedProduct, quantity, selectedOptions, calculatePrice]", [1616, 1622], "[loadOrders, page]", [2001, 2010], "[loadSettings, loadUploadedFiles, orderId]", [5634, 5668], "[onError, orderId, uploadFiles, validateFiles]"]