import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Pagination,
  Alert,
  Skeleton,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useTheme,
  useMediaQuery,
  Avatar,
  Divider,
} from '@mui/material';
import {
  History,
  TrendingUp,
  TrendingDown,
  Payment,
  AccountBalanceWallet,
  Search,
  FilterList,
  Refresh,
} from '@mui/icons-material';
import creditService, { CreditTransaction } from '../../services/creditService';

interface WalletTransactionHistoryProps {
  refreshTrigger?: number;
}

const WalletTransactionHistory: React.FC<WalletTransactionHistoryProps> = ({
  refreshTrigger = 0,
}) => {
  const [transactions, setTransactions] = useState<CreditTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    fetchTransactions(page);
  }, [page, refreshTrigger]);

  const fetchTransactions = async (pageNumber: number = 0) => {
    try {
      setLoading(true);
      setError(null);
      const response = await creditService.getTransactions(pageNumber + 1);
      setTransactions(response.transactions.data);
      setTotalPages(response.transactions.last_page);
      setTotalCount(response.transactions.total);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to load transaction history');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleRefresh = () => {
    fetchTransactions(page);
  };

  const getTransactionIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'top_up':
        return <TrendingUp color="success" />;
      case 'payment':
        return <Payment color="warning" />;
      case 'withdrawal':
        return <TrendingDown color="error" />;
      case 'refund':
        return <AccountBalanceWallet color="info" />;
      default:
        return <History color="action" />;
    }
  };

  const getStatusColor = (status: string): "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning" => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      case 'refunded':
        return 'info';
      default:
        return 'default';
    }
  };

  const getTypeColor = (type: string): "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning" => {
    switch (type.toLowerCase()) {
      case 'top_up':
        return 'success';
      case 'payment':
        return 'warning';
      case 'withdrawal':
        return 'error';
      case 'refund':
        return 'info';
      default:
        return 'default';
    }
  };

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || transaction.payment_status === statusFilter;
    const matchesType = typeFilter === 'all' || transaction.type === typeFilter;
    return matchesSearch && matchesStatus && matchesType;
  });

  if (loading) {
    return (
      <Box>
        <Typography variant="h5" gutterBottom>
          Transaction History
        </Typography>
        {[...Array(5)].map((_, index) => (
          <Card key={index} sx={{ mb: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Skeleton variant="circular" width={40} height={40} />
                <Box flex={1}>
                  <Skeleton variant="text" width="60%" />
                  <Skeleton variant="text" width="40%" />
                </Box>
                <Skeleton variant="text" width="20%" />
              </Box>
            </CardContent>
          </Card>
        ))}
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
        <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <History color="primary" />
          Transaction History
        </Typography>
        <Tooltip title="Refresh">
          <IconButton onClick={handleRefresh}>
            <Refresh />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" gap={2} flexWrap="wrap" alignItems="center">
            <TextField
              size="small"
              placeholder="Search transactions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search fontSize="small" />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 200 }}
            />
            
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                label="Status"
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <MenuItem value="all">All Status</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="failed">Failed</MenuItem>
                <MenuItem value="refunded">Refunded</MenuItem>
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Type</InputLabel>
              <Select
                value={typeFilter}
                label="Type"
                onChange={(e) => setTypeFilter(e.target.value)}
              >
                <MenuItem value="all">All Types</MenuItem>
                <MenuItem value="top_up">Top Up</MenuItem>
                <MenuItem value="payment">Payment</MenuItem>
                <MenuItem value="withdrawal">Withdrawal</MenuItem>
                <MenuItem value="refund">Refund</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </CardContent>
      </Card>

      {/* Transaction List */}
      {filteredTransactions.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <History sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No transactions found
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                ? 'Try adjusting your filters to see more results.'
                : 'Your transaction history will appear here once you make your first transaction.'}
            </Typography>
          </CardContent>
        </Card>
      ) : (
        <>
          {isMobile ? (
            // Mobile view - Card layout
            <Box>
              {filteredTransactions.map((transaction) => (
                <Card key={transaction.id} sx={{ mb: 2 }}>
                  <CardContent>
                    <Box display="flex" alignItems="flex-start" gap={2}>
                      <Avatar sx={{ bgcolor: 'primary.50' }}>
                        {getTransactionIcon(transaction.type)}
                      </Avatar>
                      
                      <Box flex={1}>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                          <Typography variant="body1" fontWeight={600}>
                            {transaction.description}
                          </Typography>
                          <Typography
                            variant="h6"
                            color={transaction.is_credit ? 'success.main' : 'error.main'}
                            sx={{ fontWeight: 600 }}
                          >
                            {transaction.is_credit ? '+' : '-'}
                            {creditService.formatWalletBalance(Math.abs(transaction.credit_amount))}
                          </Typography>
                        </Box>
                        
                        <Box display="flex" gap={1} mb={1} flexWrap="wrap">
                          <Chip
                            label={transaction.type.replace('_', ' ').toUpperCase()}
                            size="small"
                            color={getTypeColor(transaction.type)}
                          />
                          <Chip
                            label={transaction.payment_status.toUpperCase()}
                            size="small"
                            color={getStatusColor(transaction.payment_status)}
                          />
                        </Box>
                        
                        <Typography variant="caption" color="text.secondary">
                          {new Date(transaction.created_at).toLocaleString()}
                        </Typography>
                        
                        {transaction.amount_paid > 0 && (
                          <Typography variant="caption" color="text.secondary" display="block">
                            Amount Paid: {creditService.formatWalletBalance(transaction.amount_paid)}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </Box>
          ) : (
            // Desktop view - Table layout
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Type</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell align="right">Amount</TableCell>
                    <TableCell align="right">Amount Paid</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredTransactions.map((transaction) => (
                    <TableRow key={transaction.id} hover>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={1}>
                          {getTransactionIcon(transaction.type)}
                          <Chip
                            label={transaction.type.replace('_', ' ')}
                            size="small"
                            color={getTypeColor(transaction.type)}
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {transaction.description}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography
                          variant="body2"
                          fontWeight={600}
                          color={transaction.is_credit ? 'success.main' : 'error.main'}
                        >
                          {transaction.is_credit ? '+' : '-'}
                          {creditService.formatWalletBalance(Math.abs(transaction.credit_amount))}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2">
                          {transaction.amount_paid > 0 
                            ? creditService.formatWalletBalance(transaction.amount_paid)
                            : '-'
                          }
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={transaction.payment_status}
                          size="small"
                          color={getStatusColor(transaction.payment_status)}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(transaction.created_at).toLocaleDateString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(transaction.created_at).toLocaleTimeString()}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <Box display="flex" justifyContent="center" mt={3}>
              <Pagination
                count={totalPages}
                page={page + 1}
                onChange={(event, value) => handlePageChange(event, value - 1)}
                color="primary"
                showFirstButton
                showLastButton
              />
            </Box>
          )}

          {/* Summary */}
          <Card sx={{ mt: 3, backgroundColor: 'grey.50' }}>
            <CardContent>
              <Typography variant="body2" color="text.secondary">
                Showing {filteredTransactions.length} of {totalCount} transactions
              </Typography>
            </CardContent>
          </Card>
        </>
      )}
    </Box>
  );
};

export default WalletTransactionHistory;
