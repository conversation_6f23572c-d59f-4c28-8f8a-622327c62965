{"ast": null, "code": "import React from'react';import{<PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,Card,CardContent,LinearProgress,Chip,useTheme}from'@mui/material';import{Warning,Error,Info,AccountBalanceWallet,Add}from'@mui/icons-material';import creditService from'../../services/creditService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const OverdraftPrevention=_ref=>{let{currentBalance,requiredAmount=0,onTopUpClick,showAsCard=false}=_ref;const theme=useTheme();const isInsufficientFunds=requiredAmount>0&&currentBalance<requiredAmount;const shortfall=Math.max(0,requiredAmount-currentBalance);const balancePercentage=requiredAmount>0?Math.min(100,currentBalance/requiredAmount*100):100;// Determine warning level based on balance\nconst getWarningLevel=()=>{if(currentBalance<=0)return'empty';if(currentBalance<10)return'critical';if(currentBalance<50)return'low';return'sufficient';};const warningLevel=getWarningLevel();const getAlertSeverity=()=>{if(isInsufficientFunds)return'error';switch(warningLevel){case'empty':return'error';case'critical':return'warning';case'low':return'info';default:return'success';}};const getAlertIcon=()=>{if(isInsufficientFunds)return/*#__PURE__*/_jsx(Error,{});switch(warningLevel){case'empty':return/*#__PURE__*/_jsx(Error,{});case'critical':return/*#__PURE__*/_jsx(Warning,{});case'low':return/*#__PURE__*/_jsx(Info,{});default:return/*#__PURE__*/_jsx(AccountBalanceWallet,{});}};const getAlertTitle=()=>{if(isInsufficientFunds)return'Insufficient Funds';switch(warningLevel){case'empty':return'Wallet Empty';case'critical':return'Critical Balance';case'low':return'Low Balance Warning';default:return'Sufficient Balance';}};const getAlertMessage=()=>{if(isInsufficientFunds){return`You need ${creditService.formatWalletBalance(shortfall)} more to complete this transaction.`;}switch(warningLevel){case'empty':return'Your wallet is empty. Add funds to start making transactions.';case'critical':return'Your wallet balance is critically low. Consider topping up soon.';case'low':return'Your wallet balance is getting low. You may want to add more funds.';default:return'Your wallet has sufficient funds for transactions.';}};const getProgressColor=()=>{if(isInsufficientFunds)return'error';switch(warningLevel){case'empty':return'error';case'critical':return'warning';case'low':return'info';default:return'success';}};// Don't show anything if balance is sufficient and no specific amount is required\nif(!isInsufficientFunds&&warningLevel==='sufficient'&&requiredAmount===0){return null;}const content=/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Alert,{severity:getAlertSeverity(),icon:getAlertIcon(),sx:{mb:requiredAmount>0?2:0},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:600,gutterBottom:true,children:getAlertTitle()}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mb:1},children:getAlertMessage()}),/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:2,flexWrap:\"wrap\",children:[/*#__PURE__*/_jsx(Chip,{label:`Current: ${creditService.formatWalletBalance(currentBalance)}`,size:\"small\",color:getAlertSeverity(),variant:\"outlined\"}),requiredAmount>0&&/*#__PURE__*/_jsx(Chip,{label:`Required: ${creditService.formatWalletBalance(requiredAmount)}`,size:\"small\",color:\"default\",variant:\"outlined\"}),isInsufficientFunds&&/*#__PURE__*/_jsx(Chip,{label:`Shortfall: ${creditService.formatWalletBalance(shortfall)}`,size:\"small\",color:\"error\",variant:\"filled\"})]}),onTopUpClick&&(warningLevel!=='sufficient'||isInsufficientFunds)&&/*#__PURE__*/_jsx(Box,{mt:2,children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",size:\"small\",startIcon:/*#__PURE__*/_jsx(Add,{}),onClick:onTopUpClick,color:isInsufficientFunds?'error':'primary',children:\"Top Up Wallet\"})})]}),requiredAmount>0&&/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",mb:1,children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Balance Coverage\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[balancePercentage.toFixed(0),\"%\"]})]}),/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:balancePercentage,color:getProgressColor(),sx:{height:8,borderRadius:4,backgroundColor:theme.palette.grey[200],'& .MuiLinearProgress-bar':{borderRadius:4}}}),/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",mt:1,children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:creditService.formatWalletBalance(0)}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:creditService.formatWalletBalance(requiredAmount)})]})]})]});if(showAsCard){return/*#__PURE__*/_jsx(Card,{variant:\"outlined\",sx:{mb:2},children:/*#__PURE__*/_jsx(CardContent,{children:content})});}return content;};export default OverdraftPrevention;", "map": {"version": 3, "names": ["React", "Box", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "LinearProgress", "Chip", "useTheme", "Warning", "Error", "Info", "AccountBalanceWallet", "Add", "creditService", "jsx", "_jsx", "jsxs", "_jsxs", "OverdraftPrevention", "_ref", "currentBalance", "requiredAmount", "onTopUpClick", "showAsCard", "theme", "isInsufficientFunds", "shortfall", "Math", "max", "balancePercentage", "min", "getWarningLevel", "warningLevel", "getAlertSeverity", "getAlertIcon", "getAlertTitle", "getAlertMessage", "formatWalletBalance", "getProgressColor", "content", "children", "severity", "icon", "sx", "mb", "variant", "fontWeight", "gutterBottom", "display", "alignItems", "gap", "flexWrap", "label", "size", "color", "mt", "startIcon", "onClick", "justifyContent", "toFixed", "value", "height", "borderRadius", "backgroundColor", "palette", "grey"], "sources": ["C:/laragon/www/frontend/src/components/wallet/OverdraftPrevention.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  Card,\n  CardContent,\n  LinearProgress,\n  Chip,\n  useTheme,\n} from '@mui/material';\nimport {\n  Warning,\n  Error,\n  Info,\n  AccountBalanceWallet,\n  Add,\n} from '@mui/icons-material';\nimport creditService from '../../services/creditService';\n\ninterface OverdraftPreventionProps {\n  currentBalance: number;\n  requiredAmount?: number;\n  onTopUpClick?: () => void;\n  showAsCard?: boolean;\n}\n\nconst OverdraftPrevention: React.FC<OverdraftPreventionProps> = ({\n  currentBalance,\n  requiredAmount = 0,\n  onTopUpClick,\n  showAsCard = false,\n}) => {\n  const theme = useTheme();\n\n  const isInsufficientFunds = requiredAmount > 0 && currentBalance < requiredAmount;\n  const shortfall = Math.max(0, requiredAmount - currentBalance);\n  const balancePercentage = requiredAmount > 0 ? Math.min(100, (currentBalance / requiredAmount) * 100) : 100;\n\n  // Determine warning level based on balance\n  const getWarningLevel = () => {\n    if (currentBalance <= 0) return 'empty';\n    if (currentBalance < 10) return 'critical';\n    if (currentBalance < 50) return 'low';\n    return 'sufficient';\n  };\n\n  const warningLevel = getWarningLevel();\n\n  const getAlertSeverity = (): \"error\" | \"warning\" | \"info\" | \"success\" => {\n    if (isInsufficientFunds) return 'error';\n    switch (warningLevel) {\n      case 'empty':\n        return 'error';\n      case 'critical':\n        return 'warning';\n      case 'low':\n        return 'info';\n      default:\n        return 'success';\n    }\n  };\n\n  const getAlertIcon = () => {\n    if (isInsufficientFunds) return <Error />;\n    switch (warningLevel) {\n      case 'empty':\n        return <Error />;\n      case 'critical':\n        return <Warning />;\n      case 'low':\n        return <Info />;\n      default:\n        return <AccountBalanceWallet />;\n    }\n  };\n\n  const getAlertTitle = () => {\n    if (isInsufficientFunds) return 'Insufficient Funds';\n    switch (warningLevel) {\n      case 'empty':\n        return 'Wallet Empty';\n      case 'critical':\n        return 'Critical Balance';\n      case 'low':\n        return 'Low Balance Warning';\n      default:\n        return 'Sufficient Balance';\n    }\n  };\n\n  const getAlertMessage = () => {\n    if (isInsufficientFunds) {\n      return `You need ${creditService.formatWalletBalance(shortfall)} more to complete this transaction.`;\n    }\n    \n    switch (warningLevel) {\n      case 'empty':\n        return 'Your wallet is empty. Add funds to start making transactions.';\n      case 'critical':\n        return 'Your wallet balance is critically low. Consider topping up soon.';\n      case 'low':\n        return 'Your wallet balance is getting low. You may want to add more funds.';\n      default:\n        return 'Your wallet has sufficient funds for transactions.';\n    }\n  };\n\n  const getProgressColor = (): \"primary\" | \"secondary\" | \"error\" | \"info\" | \"success\" | \"warning\" => {\n    if (isInsufficientFunds) return 'error';\n    switch (warningLevel) {\n      case 'empty':\n        return 'error';\n      case 'critical':\n        return 'warning';\n      case 'low':\n        return 'info';\n      default:\n        return 'success';\n    }\n  };\n\n  // Don't show anything if balance is sufficient and no specific amount is required\n  if (!isInsufficientFunds && warningLevel === 'sufficient' && requiredAmount === 0) {\n    return null;\n  }\n\n  const content = (\n    <Box>\n      <Alert \n        severity={getAlertSeverity()} \n        icon={getAlertIcon()}\n        sx={{ mb: requiredAmount > 0 ? 2 : 0 }}\n      >\n        <Typography variant=\"body2\" fontWeight={600} gutterBottom>\n          {getAlertTitle()}\n        </Typography>\n        <Typography variant=\"body2\" sx={{ mb: 1 }}>\n          {getAlertMessage()}\n        </Typography>\n        \n        <Box display=\"flex\" alignItems=\"center\" gap={2} flexWrap=\"wrap\">\n          <Chip\n            label={`Current: ${creditService.formatWalletBalance(currentBalance)}`}\n            size=\"small\"\n            color={getAlertSeverity()}\n            variant=\"outlined\"\n          />\n          \n          {requiredAmount > 0 && (\n            <Chip\n              label={`Required: ${creditService.formatWalletBalance(requiredAmount)}`}\n              size=\"small\"\n              color=\"default\"\n              variant=\"outlined\"\n            />\n          )}\n          \n          {isInsufficientFunds && (\n            <Chip\n              label={`Shortfall: ${creditService.formatWalletBalance(shortfall)}`}\n              size=\"small\"\n              color=\"error\"\n              variant=\"filled\"\n            />\n          )}\n        </Box>\n\n        {onTopUpClick && (warningLevel !== 'sufficient' || isInsufficientFunds) && (\n          <Box mt={2}>\n            <Button\n              variant=\"contained\"\n              size=\"small\"\n              startIcon={<Add />}\n              onClick={onTopUpClick}\n              color={isInsufficientFunds ? 'error' : 'primary'}\n            >\n              Top Up Wallet\n            </Button>\n          </Box>\n        )}\n      </Alert>\n\n      {/* Progress bar for required amount */}\n      {requiredAmount > 0 && (\n        <Box>\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={1}>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Balance Coverage\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {balancePercentage.toFixed(0)}%\n            </Typography>\n          </Box>\n          \n          <LinearProgress\n            variant=\"determinate\"\n            value={balancePercentage}\n            color={getProgressColor()}\n            sx={{\n              height: 8,\n              borderRadius: 4,\n              backgroundColor: theme.palette.grey[200],\n              '& .MuiLinearProgress-bar': {\n                borderRadius: 4,\n              },\n            }}\n          />\n          \n          <Box display=\"flex\" justifyContent=\"space-between\" mt={1}>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {creditService.formatWalletBalance(0)}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {creditService.formatWalletBalance(requiredAmount)}\n            </Typography>\n          </Box>\n        </Box>\n      )}\n    </Box>\n  );\n\n  if (showAsCard) {\n    return (\n      <Card variant=\"outlined\" sx={{ mb: 2 }}>\n        <CardContent>\n          {content}\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return content;\n};\n\nexport default OverdraftPrevention;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,GAAG,CACHC,KAAK,CACLC,UAAU,CACVC,MAAM,CACNC,IAAI,CACJC,WAAW,CACXC,cAAc,CACdC,IAAI,CACJC,QAAQ,KACH,eAAe,CACtB,OACEC,OAAO,CACPC,KAAK,CACLC,IAAI,CACJC,oBAAoB,CACpBC,GAAG,KACE,qBAAqB,CAC5B,MAAO,CAAAC,aAAa,KAAM,8BAA8B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBASzD,KAAM,CAAAC,mBAAuD,CAAGC,IAAA,EAK1D,IAL2D,CAC/DC,cAAc,CACdC,cAAc,CAAG,CAAC,CAClBC,YAAY,CACZC,UAAU,CAAG,KACf,CAAC,CAAAJ,IAAA,CACC,KAAM,CAAAK,KAAK,CAAGjB,QAAQ,CAAC,CAAC,CAExB,KAAM,CAAAkB,mBAAmB,CAAGJ,cAAc,CAAG,CAAC,EAAID,cAAc,CAAGC,cAAc,CACjF,KAAM,CAAAK,SAAS,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEP,cAAc,CAAGD,cAAc,CAAC,CAC9D,KAAM,CAAAS,iBAAiB,CAAGR,cAAc,CAAG,CAAC,CAAGM,IAAI,CAACG,GAAG,CAAC,GAAG,CAAGV,cAAc,CAAGC,cAAc,CAAI,GAAG,CAAC,CAAG,GAAG,CAE3G;AACA,KAAM,CAAAU,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAIX,cAAc,EAAI,CAAC,CAAE,MAAO,OAAO,CACvC,GAAIA,cAAc,CAAG,EAAE,CAAE,MAAO,UAAU,CAC1C,GAAIA,cAAc,CAAG,EAAE,CAAE,MAAO,KAAK,CACrC,MAAO,YAAY,CACrB,CAAC,CAED,KAAM,CAAAY,YAAY,CAAGD,eAAe,CAAC,CAAC,CAEtC,KAAM,CAAAE,gBAAgB,CAAGA,CAAA,GAAgD,CACvE,GAAIR,mBAAmB,CAAE,MAAO,OAAO,CACvC,OAAQO,YAAY,EAClB,IAAK,OAAO,CACV,MAAO,OAAO,CAChB,IAAK,UAAU,CACb,MAAO,SAAS,CAClB,IAAK,KAAK,CACR,MAAO,MAAM,CACf,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED,KAAM,CAAAE,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIT,mBAAmB,CAAE,mBAAOV,IAAA,CAACN,KAAK,GAAE,CAAC,CACzC,OAAQuB,YAAY,EAClB,IAAK,OAAO,CACV,mBAAOjB,IAAA,CAACN,KAAK,GAAE,CAAC,CAClB,IAAK,UAAU,CACb,mBAAOM,IAAA,CAACP,OAAO,GAAE,CAAC,CACpB,IAAK,KAAK,CACR,mBAAOO,IAAA,CAACL,IAAI,GAAE,CAAC,CACjB,QACE,mBAAOK,IAAA,CAACJ,oBAAoB,GAAE,CAAC,CACnC,CACF,CAAC,CAED,KAAM,CAAAwB,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAIV,mBAAmB,CAAE,MAAO,oBAAoB,CACpD,OAAQO,YAAY,EAClB,IAAK,OAAO,CACV,MAAO,cAAc,CACvB,IAAK,UAAU,CACb,MAAO,kBAAkB,CAC3B,IAAK,KAAK,CACR,MAAO,qBAAqB,CAC9B,QACE,MAAO,oBAAoB,CAC/B,CACF,CAAC,CAED,KAAM,CAAAI,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAIX,mBAAmB,CAAE,CACvB,MAAO,YAAYZ,aAAa,CAACwB,mBAAmB,CAACX,SAAS,CAAC,qCAAqC,CACtG,CAEA,OAAQM,YAAY,EAClB,IAAK,OAAO,CACV,MAAO,+DAA+D,CACxE,IAAK,UAAU,CACb,MAAO,kEAAkE,CAC3E,IAAK,KAAK,CACR,MAAO,qEAAqE,CAC9E,QACE,MAAO,oDAAoD,CAC/D,CACF,CAAC,CAED,KAAM,CAAAM,gBAAgB,CAAGA,CAAA,GAA0E,CACjG,GAAIb,mBAAmB,CAAE,MAAO,OAAO,CACvC,OAAQO,YAAY,EAClB,IAAK,OAAO,CACV,MAAO,OAAO,CAChB,IAAK,UAAU,CACb,MAAO,SAAS,CAClB,IAAK,KAAK,CACR,MAAO,MAAM,CACf,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED;AACA,GAAI,CAACP,mBAAmB,EAAIO,YAAY,GAAK,YAAY,EAAIX,cAAc,GAAK,CAAC,CAAE,CACjF,MAAO,KAAI,CACb,CAEA,KAAM,CAAAkB,OAAO,cACXtB,KAAA,CAAClB,GAAG,EAAAyC,QAAA,eACFvB,KAAA,CAACjB,KAAK,EACJyC,QAAQ,CAAER,gBAAgB,CAAC,CAAE,CAC7BS,IAAI,CAAER,YAAY,CAAC,CAAE,CACrBS,EAAE,CAAE,CAAEC,EAAE,CAAEvB,cAAc,CAAG,CAAC,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAmB,QAAA,eAEvCzB,IAAA,CAACd,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAACC,UAAU,CAAE,GAAI,CAACC,YAAY,MAAAP,QAAA,CACtDL,aAAa,CAAC,CAAC,CACN,CAAC,cACbpB,IAAA,CAACd,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAACF,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CACvCJ,eAAe,CAAC,CAAC,CACR,CAAC,cAEbnB,KAAA,CAAClB,GAAG,EAACiD,OAAO,CAAC,MAAM,CAACC,UAAU,CAAC,QAAQ,CAACC,GAAG,CAAE,CAAE,CAACC,QAAQ,CAAC,MAAM,CAAAX,QAAA,eAC7DzB,IAAA,CAACT,IAAI,EACH8C,KAAK,CAAE,YAAYvC,aAAa,CAACwB,mBAAmB,CAACjB,cAAc,CAAC,EAAG,CACvEiC,IAAI,CAAC,OAAO,CACZC,KAAK,CAAErB,gBAAgB,CAAC,CAAE,CAC1BY,OAAO,CAAC,UAAU,CACnB,CAAC,CAEDxB,cAAc,CAAG,CAAC,eACjBN,IAAA,CAACT,IAAI,EACH8C,KAAK,CAAE,aAAavC,aAAa,CAACwB,mBAAmB,CAAChB,cAAc,CAAC,EAAG,CACxEgC,IAAI,CAAC,OAAO,CACZC,KAAK,CAAC,SAAS,CACfT,OAAO,CAAC,UAAU,CACnB,CACF,CAEApB,mBAAmB,eAClBV,IAAA,CAACT,IAAI,EACH8C,KAAK,CAAE,cAAcvC,aAAa,CAACwB,mBAAmB,CAACX,SAAS,CAAC,EAAG,CACpE2B,IAAI,CAAC,OAAO,CACZC,KAAK,CAAC,OAAO,CACbT,OAAO,CAAC,QAAQ,CACjB,CACF,EACE,CAAC,CAELvB,YAAY,GAAKU,YAAY,GAAK,YAAY,EAAIP,mBAAmB,CAAC,eACrEV,IAAA,CAAChB,GAAG,EAACwD,EAAE,CAAE,CAAE,CAAAf,QAAA,cACTzB,IAAA,CAACb,MAAM,EACL2C,OAAO,CAAC,WAAW,CACnBQ,IAAI,CAAC,OAAO,CACZG,SAAS,cAAEzC,IAAA,CAACH,GAAG,GAAE,CAAE,CACnB6C,OAAO,CAAEnC,YAAa,CACtBgC,KAAK,CAAE7B,mBAAmB,CAAG,OAAO,CAAG,SAAU,CAAAe,QAAA,CAClD,eAED,CAAQ,CAAC,CACN,CACN,EACI,CAAC,CAGPnB,cAAc,CAAG,CAAC,eACjBJ,KAAA,CAAClB,GAAG,EAAAyC,QAAA,eACFvB,KAAA,CAAClB,GAAG,EAACiD,OAAO,CAAC,MAAM,CAACU,cAAc,CAAC,eAAe,CAACT,UAAU,CAAC,QAAQ,CAACL,EAAE,CAAE,CAAE,CAAAJ,QAAA,eAC3EzB,IAAA,CAACd,UAAU,EAAC4C,OAAO,CAAC,SAAS,CAACS,KAAK,CAAC,gBAAgB,CAAAd,QAAA,CAAC,kBAErD,CAAY,CAAC,cACbvB,KAAA,CAAChB,UAAU,EAAC4C,OAAO,CAAC,SAAS,CAACS,KAAK,CAAC,gBAAgB,CAAAd,QAAA,EACjDX,iBAAiB,CAAC8B,OAAO,CAAC,CAAC,CAAC,CAAC,GAChC,EAAY,CAAC,EACV,CAAC,cAEN5C,IAAA,CAACV,cAAc,EACbwC,OAAO,CAAC,aAAa,CACrBe,KAAK,CAAE/B,iBAAkB,CACzByB,KAAK,CAAEhB,gBAAgB,CAAC,CAAE,CAC1BK,EAAE,CAAE,CACFkB,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,CAAC,CACfC,eAAe,CAAEvC,KAAK,CAACwC,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC,CACxC,0BAA0B,CAAE,CAC1BH,YAAY,CAAE,CAChB,CACF,CAAE,CACH,CAAC,cAEF7C,KAAA,CAAClB,GAAG,EAACiD,OAAO,CAAC,MAAM,CAACU,cAAc,CAAC,eAAe,CAACH,EAAE,CAAE,CAAE,CAAAf,QAAA,eACvDzB,IAAA,CAACd,UAAU,EAAC4C,OAAO,CAAC,SAAS,CAACS,KAAK,CAAC,gBAAgB,CAAAd,QAAA,CACjD3B,aAAa,CAACwB,mBAAmB,CAAC,CAAC,CAAC,CAC3B,CAAC,cACbtB,IAAA,CAACd,UAAU,EAAC4C,OAAO,CAAC,SAAS,CAACS,KAAK,CAAC,gBAAgB,CAAAd,QAAA,CACjD3B,aAAa,CAACwB,mBAAmB,CAAChB,cAAc,CAAC,CACxC,CAAC,EACV,CAAC,EACH,CACN,EACE,CACN,CAED,GAAIE,UAAU,CAAE,CACd,mBACER,IAAA,CAACZ,IAAI,EAAC0C,OAAO,CAAC,UAAU,CAACF,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cACrCzB,IAAA,CAACX,WAAW,EAAAoC,QAAA,CACTD,OAAO,CACG,CAAC,CACV,CAAC,CAEX,CAEA,MAAO,CAAAA,OAAO,CAChB,CAAC,CAED,cAAe,CAAArB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}