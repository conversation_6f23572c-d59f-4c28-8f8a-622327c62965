# Admin Account Setup Guide

## Overview

This guide provides multiple methods to create admin accounts for your wallet-based application. Choose the method that best fits your deployment scenario.

## 🔐 Password Information

**Default Password Hash**: `$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi`
- **Plain Text**: `password`
- **Hash Type**: <PERSON><PERSON> bcrypt (cost: 12)
- **⚠️ IMPORTANT**: Change this password immediately after first login in production!

## 📋 Setup Methods

### Method 1: Quick Single Admin (Recommended)

```sql
-- Create main admin account
INSERT INTO `users` (
    `name`, `email`, `email_verified_at`, `password`, 
    `role`, `is_active`, `wallet_balance`, `created_at`, `updated_at`
) VALUES (
    'System Administrator',
    '<EMAIL>',  -- ⚠️ Change this email
    NOW(),
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'admin',
    1,
    1000.00,  -- Initial RM 1000 wallet balance
    NOW(),
    NOW()
);
```

### Method 2: Multiple Admin Roles

```sql
-- Super Admin
INSERT INTO `users` (`name`, `email`, `email_verified_at`, `password`, `role`, `is_active`, `wallet_balance`, `created_at`, `updated_at`) 
VALUES ('Super Admin', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1, 0.00, NOW(), NOW());

-- Customer Support
INSERT INTO `users` (`name`, `email`, `email_verified_at`, `password`, `role`, `is_active`, `wallet_balance`, `created_at`, `updated_at`) 
VALUES ('Customer Support', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1, 0.00, NOW(), NOW());

-- Finance Manager
INSERT INTO `users` (`name`, `email`, `email_verified_at`, `password`, `role`, `is_active`, `wallet_balance`, `created_at`, `updated_at`) 
VALUES ('Finance Manager', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1, 0.00, NOW(), NOW());
```

### Method 3: Development/Testing Accounts

```sql
-- Development Admin (localhost only)
INSERT INTO `users` (`name`, `email`, `email_verified_at`, `password`, `role`, `is_active`, `wallet_balance`, `created_at`, `updated_at`) 
VALUES ('Dev Admin', 'dev@localhost', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1, 9999.99, NOW(), NOW());

-- Test User
INSERT INTO `users` (`name`, `email`, `email_verified_at`, `password`, `role`, `is_active`, `wallet_balance`, `created_at`, `updated_at`) 
VALUES ('Test User', 'test@localhost', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 1, 100.00, NOW(), NOW());
```

## 🚀 Implementation Steps

### Option A: Add to Main Schema

1. **Edit schema.sql** and add admin accounts at the end:
   ```sql
   -- Add this at the end of schema.sql before the final comments
   
   -- =====================================================
   -- INITIAL ADMIN ACCOUNT
   -- =====================================================
   
   INSERT INTO `users` (
       `name`, `email`, `email_verified_at`, `password`, 
       `role`, `is_active`, `wallet_balance`, `created_at`, `updated_at`
   ) VALUES (
       'System Administrator',
       '<EMAIL>',
       NOW(),
       '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
       'admin',
       1,
       1000.00,
       NOW(),
       NOW()
   );
   ```

### Option B: Separate Admin Setup File

1. **Import schema first**:
   ```bash
   mysql -u username -p database_name < schema.sql
   ```

2. **Import admin accounts**:
   ```bash
   mysql -u username -p database_name < admin-account-setup.sql
   ```

### Option C: Laravel Seeder (Recommended for Laravel)

1. **Create seeder**:
   ```bash
   cd backend
   php artisan make:seeder AdminUserSeeder
   ```

2. **Edit the seeder** (`backend/database/seeders/AdminUserSeeder.php`):
   ```php
   <?php
   
   use Illuminate\Database\Seeder;
   use Illuminate\Support\Facades\Hash;
   use App\Models\User;
   
   class AdminUserSeeder extends Seeder
   {
       public function run()
       {
           User::create([
               'name' => 'System Administrator',
               'email' => '<EMAIL>',
               'email_verified_at' => now(),
               'password' => Hash::make('your-secure-password'),
               'role' => 'admin',
               'is_active' => true,
               'wallet_balance' => 1000.00,
           ]);
       }
   }
   ```

3. **Run seeder**:
   ```bash
   php artisan db:seed --class=AdminUserSeeder
   ```

## 🔍 Verification Commands

### Check Admin Accounts
```sql
-- List all admin accounts
SELECT id, name, email, role, is_active, wallet_balance, created_at 
FROM users 
WHERE role = 'admin';
```

### Check User Counts
```sql
-- Count users by role
SELECT role, COUNT(*) as count 
FROM users 
GROUP BY role;
```

### Check Wallet Balances
```sql
-- Admin wallet balances
SELECT name, email, wallet_balance 
FROM users 
WHERE role = 'admin' 
ORDER BY wallet_balance DESC;
```

## 🔒 Security Best Practices

### 1. Change Default Passwords
```sql
-- Update admin password (replace with actual bcrypt hash)
UPDATE users 
SET password = '$2y$12$NEW_BCRYPT_HASH_HERE' 
WHERE email = '<EMAIL>';
```

### 2. Use Strong Emails
- ✅ `<EMAIL>`
- ✅ `<EMAIL>`
- ❌ `<EMAIL>`
- ❌ `admin@localhost` (production)

### 3. Set Appropriate Wallet Balances
- **Production Admin**: `0.00` (no wallet balance needed)
- **Development Admin**: `1000.00` (for testing)
- **Test Users**: `100.00` (for testing purchases)

### 4. Email Verification
```sql
-- Verify admin email immediately
UPDATE users 
SET email_verified_at = NOW() 
WHERE role = 'admin' AND email_verified_at IS NULL;
```

## 🧪 Testing Admin Access

### 1. Login Test
- **URL**: `http://yourapp.com/admin`
- **Email**: `<EMAIL>`
- **Password**: `password` (change immediately!)

### 2. Admin Dashboard Access
- Navigate to `/admin/users`
- Check if you can see the "Balance" column
- Verify wallet transaction history

### 3. Wallet Functionality Test
```sql
-- Add test transaction for admin
INSERT INTO wallet_transactions (
    user_id, type, amount, payment_status, description, processed_at, created_at, updated_at
) VALUES (
    1, 'bonus', 500.00, 'completed', 'Admin test transaction', NOW(), NOW(), NOW()
);
```

## 🚨 Production Checklist

- [ ] Changed default password
- [ ] Used company email domain
- [ ] Set appropriate wallet balance (usually 0.00 for admin)
- [ ] Verified email address
- [ ] Tested admin login
- [ ] Tested admin dashboard access
- [ ] Removed development/test accounts
- [ ] Documented admin credentials securely

## 📞 Troubleshooting

### Issue: Can't login to admin
```sql
-- Check if admin exists and is active
SELECT * FROM users WHERE email = '<EMAIL>';

-- Activate admin if needed
UPDATE users SET is_active = 1 WHERE email = '<EMAIL>';
```

### Issue: Password not working
```bash
# Generate new password hash in Laravel
cd backend
php artisan tinker
>>> Hash::make('your-new-password')
```

### Issue: Admin can't access admin panel
```sql
-- Ensure role is set correctly
UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';
```

---

**⚠️ Security Warning**: Always change default passwords and use secure email addresses in production environments!
