import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Grid,
  IconButton,
  Tooltip,
  Chip,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Add,
  History,
  Refresh,
  AccountBalanceWallet,
  TrendingUp,
  Payment,
  Speed,
} from '@mui/icons-material';
import creditService from '../../services/creditService';

interface WalletQuickActionsProps {
  currentBalance: number;
  onTopUpClick?: () => void;
  onHistoryClick?: () => void;
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

interface QuickTopUpOption {
  amount: number;
  label: string;
  popular?: boolean;
}

const WalletQuickActions: React.FC<WalletQuickActionsProps> = ({
  currentBalance,
  onTopUpClick,
  onHistoryClick,
  onRefresh,
  isRefreshing = false,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const quickTopUpOptions: QuickTopUpOption[] = [
    { amount: 20, label: 'RM 20', popular: true },
    { amount: 50, label: 'RM 50', popular: true },
    { amount: 100, label: 'RM 100' },
  ];

  const handleQuickTopUp = (amount: number) => {
    // For now, just redirect to the top-up tab
    // In a real implementation, this could open a quick top-up modal
    if (onTopUpClick) {
      onTopUpClick();
    }
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Speed color="primary" />
            Quick Actions
          </Typography>
          
          <Tooltip title="Refresh Balance">
            <IconButton 
              onClick={onRefresh}
              disabled={isRefreshing}
              size="small"
            >
              <Refresh 
                sx={{ 
                  animation: isRefreshing ? 'spin 1s linear infinite' : 'none',
                  '@keyframes spin': {
                    '0%': { transform: 'rotate(0deg)' },
                    '100%': { transform: 'rotate(360deg)' },
                  }
                }} 
              />
            </IconButton>
          </Tooltip>
        </Box>

        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
            gap: 2
          }}
        >
          {/* Main Actions */}
          <Box display="flex" flexDirection="column" gap={2}>
            <Button
              variant="contained"
              size="large"
              startIcon={<Add />}
              onClick={onTopUpClick}
              fullWidth
              sx={{
                py: 1.5,
                background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
                '&:hover': {
                  background: 'linear-gradient(45deg, #1565c0 30%, #1976d2 90%)',
                }
              }}
            >
              Top Up Wallet
            </Button>

            <Button
              variant="outlined"
              size="large"
              startIcon={<History />}
              onClick={onHistoryClick}
              fullWidth
              sx={{ py: 1.5 }}
            >
              View History
            </Button>
          </Box>

          {/* Quick Top-Up Options */}
          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Quick Top-Up
            </Typography>

            <Box display="flex" flexDirection="column" gap={1}>
              {quickTopUpOptions.map((option) => (
                <Button
                  key={option.amount}
                  variant="text"
                  size="small"
                  onClick={() => handleQuickTopUp(option.amount)}
                  sx={{
                    justifyContent: 'flex-start',
                    textTransform: 'none',
                    color: 'text.primary',
                    '&:hover': {
                      backgroundColor: 'primary.50',
                    }
                  }}
                  startIcon={<TrendingUp fontSize="small" />}
                  endIcon={option.popular ? (
                    <Chip
                      label="Popular"
                      size="small"
                      color="primary"
                      sx={{ height: 20, fontSize: '0.7rem' }}
                    />
                  ) : null}
                >
                  {option.label}
                </Button>
              ))}
            </Box>
          </Box>
        </Box>

        {/* Balance Status */}
        <Box 
          mt={3} 
          p={2} 
          sx={{ 
            backgroundColor: 'grey.50',
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'grey.200',
          }}
        >
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center" gap={1}>
              <AccountBalanceWallet color="primary" fontSize="small" />
              <Typography variant="body2" color="text.secondary">
                Current Balance
              </Typography>
            </Box>
            
            <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
              {creditService.formatWalletBalance(currentBalance)}
            </Typography>
          </Box>
          
          {/* Balance Status Indicator */}
          <Box mt={1}>
            {currentBalance <= 0 ? (
              <Chip 
                label="Empty Wallet" 
                color="error" 
                size="small"
                icon={<AccountBalanceWallet />}
              />
            ) : currentBalance < 10 ? (
              <Chip 
                label="Low Balance" 
                color="warning" 
                size="small"
                icon={<AccountBalanceWallet />}
              />
            ) : currentBalance < 50 ? (
              <Chip 
                label="Moderate Balance" 
                color="info" 
                size="small"
                icon={<AccountBalanceWallet />}
              />
            ) : (
              <Chip 
                label="Good Balance" 
                color="success" 
                size="small"
                icon={<AccountBalanceWallet />}
              />
            )}
          </Box>
        </Box>

        {/* Payment Security Notice */}
        <Box mt={2}>
          <Typography variant="caption" color="text.secondary" display="flex" alignItems="center" gap={0.5}>
            <Payment fontSize="small" />
            Secure payments powered by Billplz
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default WalletQuickActions;
