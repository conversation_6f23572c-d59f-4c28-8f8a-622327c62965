#!/usr/bin/env node

/**
 * Database Schema Validation Test
 * This script validates the generated schema.sql file
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Database Schema Validation Test');
console.log('===================================\n');

let allTestsPassed = true;

// Test 1: Check if schema.sql exists and is readable
function testSchemaFileExists() {
    console.log('1. Testing Schema File Existence...');
    
    const schemaPath = path.join(__dirname, 'schema.sql');
    const exists = fs.existsSync(schemaPath);
    
    if (exists) {
        const stats = fs.statSync(schemaPath);
        const sizeKB = Math.round(stats.size / 1024);
        console.log(`   ✅ Schema file exists: ${exists}`);
        console.log(`   ✅ File size: ${sizeKB} KB`);
        console.log('   🎉 Schema file existence: PASS\n');
        return true;
    } else {
        console.log(`   ❌ Schema file exists: ${exists}`);
        console.log('   ❌ Schema file existence: FAIL\n');
        return false;
    }
}

// Test 2: Validate schema content structure
function testSchemaContent() {
    console.log('2. Testing Schema Content Structure...');
    
    try {
        const schemaPath = path.join(__dirname, 'schema.sql');
        const content = fs.readFileSync(schemaPath, 'utf8');
        
        // Check for essential wallet system tables
        const hasUsersTable = content.includes('CREATE TABLE `users`');
        const hasWalletTransactions = content.includes('CREATE TABLE `wallet_transactions`');
        const hasCreditPackages = content.includes('CREATE TABLE `credit_packages`');
        const hasPaymentSettings = content.includes('CREATE TABLE `payment_settings`');
        
        // Check for wallet_balance field (not credit_balance)
        const hasWalletBalance = content.includes('`wallet_balance` decimal(10,2)');
        const noOldCreditBalance = !content.includes('`credit_balance`');
        
        // Check for proper currency precision
        const hasProperCurrency = content.includes('decimal(10,2)');
        
        // Check for foreign key constraints
        const hasForeignKeys = content.includes('FOREIGN KEY') && content.includes('REFERENCES');
        
        // Check for indexes
        const hasIndexes = content.includes('KEY ') && content.includes('INDEX');
        
        console.log(`   ✅ Users table: ${hasUsersTable}`);
        console.log(`   ✅ Wallet transactions table: ${hasWalletTransactions}`);
        console.log(`   ✅ Credit packages table: ${hasCreditPackages}`);
        console.log(`   ✅ Payment settings table: ${hasPaymentSettings}`);
        console.log(`   ✅ Wallet balance field: ${hasWalletBalance}`);
        console.log(`   ✅ No old credit_balance: ${noOldCreditBalance}`);
        console.log(`   ✅ Proper currency precision: ${hasProperCurrency}`);
        console.log(`   ✅ Foreign key constraints: ${hasForeignKeys}`);
        console.log(`   ✅ Performance indexes: ${hasIndexes}`);
        
        const allChecks = hasUsersTable && hasWalletTransactions && hasCreditPackages && 
                         hasPaymentSettings && hasWalletBalance && noOldCreditBalance && 
                         hasProperCurrency && hasForeignKeys && hasIndexes;
        
        if (allChecks) {
            console.log('   🎉 Schema content structure: PASS\n');
            return true;
        } else {
            console.log('   ❌ Schema content structure: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error reading schema: ${error.message}\n`);
        return false;
    }
}

// Test 3: Check for printing system tables
function testPrintingSystemTables() {
    console.log('3. Testing Printing System Tables...');
    
    try {
        const schemaPath = path.join(__dirname, 'schema.sql');
        const content = fs.readFileSync(schemaPath, 'utf8');
        
        const hasPrintingCategories = content.includes('CREATE TABLE `printing_categories`');
        const hasPrintingProducts = content.includes('CREATE TABLE `printing_products`');
        const hasPrintingOrders = content.includes('CREATE TABLE `printing_orders`');
        const hasOrderItems = content.includes('CREATE TABLE `order_items`');
        const hasOrderFiles = content.includes('CREATE TABLE `order_files`');
        
        console.log(`   ✅ Printing categories: ${hasPrintingCategories}`);
        console.log(`   ✅ Printing products: ${hasPrintingProducts}`);
        console.log(`   ✅ Printing orders: ${hasPrintingOrders}`);
        console.log(`   ✅ Order items: ${hasOrderItems}`);
        console.log(`   ✅ Order files: ${hasOrderFiles}`);
        
        const allPrintingTables = hasPrintingCategories && hasPrintingProducts && 
                                 hasPrintingOrders && hasOrderItems && hasOrderFiles;
        
        if (allPrintingTables) {
            console.log('   🎉 Printing system tables: PASS\n');
            return true;
        } else {
            console.log('   ❌ Printing system tables: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error checking printing tables: ${error.message}\n`);
        return false;
    }
}

// Test 4: Check for CMS and email system tables
function testCMSAndEmailTables() {
    console.log('4. Testing CMS and Email System Tables...');
    
    try {
        const schemaPath = path.join(__dirname, 'schema.sql');
        const content = fs.readFileSync(schemaPath, 'utf8');
        
        const hasCMSPages = content.includes('CREATE TABLE `cms_pages`');
        const hasEmailTemplates = content.includes('CREATE TABLE `email_templates`');
        const hasEmailNotifications = content.includes('CREATE TABLE `email_notifications`');
        const hasFileUploadSettings = content.includes('CREATE TABLE `file_upload_settings`');
        
        console.log(`   ✅ CMS pages: ${hasCMSPages}`);
        console.log(`   ✅ Email templates: ${hasEmailTemplates}`);
        console.log(`   ✅ Email notifications: ${hasEmailNotifications}`);
        console.log(`   ✅ File upload settings: ${hasFileUploadSettings}`);
        
        const allSystemTables = hasCMSPages && hasEmailTemplates && 
                               hasEmailNotifications && hasFileUploadSettings;
        
        if (allSystemTables) {
            console.log('   🎉 CMS and email system tables: PASS\n');
            return true;
        } else {
            console.log('   ❌ CMS and email system tables: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error checking system tables: ${error.message}\n`);
        return false;
    }
}

// Test 5: Check for wallet system specific features
function testWalletSystemFeatures() {
    console.log('5. Testing Wallet System Specific Features...');
    
    try {
        const schemaPath = path.join(__dirname, 'schema.sql');
        const content = fs.readFileSync(schemaPath, 'utf8');
        
        // Check for wallet transaction types
        const hasWalletTransactionTypes = content.includes("'top_up','payment','withdrawal','refund','bonus','adjustment'");
        
        // Check for MYR currency comments
        const hasMYRComments = content.includes('MYR');
        
        // Check for Billplz integration fields
        const hasBillplzFields = content.includes('payment_reference') && content.includes('payment_status');
        
        // Check for withdrawal support
        const hasWithdrawalSupport = content.includes('withdrawal_method') && content.includes('withdrawal_processed_at');
        
        // Check for proper decimal precision
        const hasDecimalPrecision = content.includes('decimal(10,2)');
        
        console.log(`   ✅ Wallet transaction types: ${hasWalletTransactionTypes}`);
        console.log(`   ✅ MYR currency references: ${hasMYRComments}`);
        console.log(`   ✅ Billplz integration fields: ${hasBillplzFields}`);
        console.log(`   ✅ Withdrawal support: ${hasWithdrawalSupport}`);
        console.log(`   ✅ Decimal precision: ${hasDecimalPrecision}`);
        
        const allWalletFeatures = hasWalletTransactionTypes && hasMYRComments && 
                                 hasBillplzFields && hasWithdrawalSupport && hasDecimalPrecision;
        
        if (allWalletFeatures) {
            console.log('   🎉 Wallet system features: PASS\n');
            return true;
        } else {
            console.log('   ❌ Wallet system features: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error checking wallet features: ${error.message}\n`);
        return false;
    }
}

// Run all tests
const test1 = testSchemaFileExists();
const test2 = testSchemaContent();
const test3 = testPrintingSystemTables();
const test4 = testCMSAndEmailTables();
const test5 = testWalletSystemFeatures();

allTestsPassed = test1 && test2 && test3 && test4 && test5;

// Summary
console.log('📊 Test Summary');
console.log('================');
console.log(`Total Tests: 5`);
console.log(`Passed: ${[test1, test2, test3, test4, test5].filter(Boolean).length}`);
console.log(`Failed: ${[test1, test2, test3, test4, test5].filter(t => !t).length}`);
console.log(`Success Rate: ${Math.round(([test1, test2, test3, test4, test5].filter(Boolean).length / 5) * 100)}%`);

if (allTestsPassed) {
    console.log('\n🎉 All tests passed! Database schema is complete and valid.');
    console.log('\n✅ Schema Features:');
    console.log('   - Wallet-based monetary system with MYR currency');
    console.log('   - Complete user management with wallet balance');
    console.log('   - Comprehensive transaction tracking');
    console.log('   - Billplz payment gateway integration');
    console.log('   - Full printing order management system');
    console.log('   - CMS and email notification system');
    console.log('   - Proper foreign key constraints and indexes');
    console.log('   - Migration-ready with DROP TABLE IF EXISTS statements');
} else {
    console.log('\n❌ Some tests failed. Please check the schema implementation.');
}
