{"ast": null, "code": "import React,{useState,useEffect,useCallback}from'react';import{Box,Typography,Paper,Button,LinearProgress,Alert,Chip,IconButton,Dialog,DialogTitle,DialogContent,DialogActions,Card,CardContent,CardActions,Tooltip}from'@mui/material';import{CloudUpload as UploadIcon,Delete as DeleteIcon,Warning as WarningIcon,CheckCircle as CheckIcon,Error as ErrorIcon,Image as ImageIcon,PictureAsPdf as PdfIcon,Description as FileIcon}from'@mui/icons-material';import{useDropzone}from'react-dropzone';import printingService from'../../services/printingService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FileUpload=_ref=>{let{orderId,onFilesUploaded,onError}=_ref;const[settings,setSettings]=useState(null);const[uploadedFiles,setUploadedFiles]=useState([]);const[uploadingFiles,setUploadingFiles]=useState([]);const[loading,setLoading]=useState(false);const[deleteDialogOpen,setDeleteDialogOpen]=useState(false);const[fileToDelete,setFileToDelete]=useState(null);// Load settings and files on component mount\nuseEffect(()=>{loadSettings();if(orderId){loadUploadedFiles();}},[orderId]);const loadSettings=async()=>{try{const uploadSettings=await printingService.getUploadSettings();// Ensure allowed_file_types is always an array\nif(uploadSettings&&!Array.isArray(uploadSettings.allowed_file_types)){uploadSettings.allowed_file_types=['pdf','png','jpg','jpeg'];}setSettings(uploadSettings);}catch(error){console.error('Failed to load upload settings:',error);onError===null||onError===void 0?void 0:onError('Failed to load upload settings');// Set default settings if loading fails\nsetSettings({allowed_file_types:['pdf','png','jpg','jpeg'],max_file_size_mb:50,max_total_upload_size_mb:200,max_files_per_order:10,min_dpi_requirement:300,dpi_warning_threshold:150,enable_dpi_validation:true,min_width_px:100,min_height_px:100,max_width_px:10000,max_height_px:10000,enable_dimension_validation:true});}};const loadUploadedFiles=async()=>{if(!orderId)return;try{const files=await printingService.getOrderFiles(orderId);setUploadedFiles(files);}catch(error){console.error('Failed to load uploaded files:',error);}};// Validate files before upload\nconst validateFiles=async files=>{if(!settings)return{valid:[],invalid:[]};const valid=[];const invalid=[];// Check total file count\nconst totalFiles=uploadedFiles.length+files.length;if(totalFiles>settings.max_files_per_order){const excess=totalFiles-settings.max_files_per_order;for(let i=files.length-excess;i<files.length;i++){invalid.push({file:files[i],errors:[`Maximum ${settings.max_files_per_order} files allowed per order`]});}files=files.slice(0,files.length-excess);}// Check total upload size\nconst currentTotalSize=uploadedFiles.reduce((sum,file)=>sum+file.file_size,0);const newFilesSize=files.reduce((sum,file)=>sum+file.size,0);const totalSize=currentTotalSize+newFilesSize;const maxTotalSize=settings.max_total_upload_size_mb*1024*1024;if(totalSize>maxTotalSize){onError===null||onError===void 0?void 0:onError(`Total upload size would exceed ${settings.max_total_upload_size_mb}MB limit`);return{valid:[],invalid:files.map(file=>({file,errors:['Total size limit exceeded']}))};}// Validate each file\nfor(const file of files){const validation=await printingService.validateFile(file);if(validation.valid){valid.push(file);}else{invalid.push({file,errors:validation.message?[validation.message]:['Validation failed']});}}return{valid,invalid};};const onDrop=useCallback(async acceptedFiles=>{if(!orderId){onError===null||onError===void 0?void 0:onError('Please create an order first before uploading files');return;}const{valid,invalid}=await validateFiles(acceptedFiles);// Show errors for invalid files\nif(invalid.length>0){const errorMessages=invalid.map(_ref2=>{let{file,errors}=_ref2;return`${file.name}: ${errors.join(', ')}`;}).join('\\n');onError===null||onError===void 0?void 0:onError(errorMessages);}// Upload valid files\nif(valid.length>0){await uploadFiles(valid);}},[orderId,settings,uploadedFiles]);const uploadFiles=async files=>{setLoading(true);// Initialize uploading files state\nconst newUploadingFiles=files.map(file=>({file,progress:0,status:'uploading'}));setUploadingFiles(newUploadingFiles);try{const uploadedFileResults=await printingService.uploadFiles(orderId,files,'artwork');// Update uploading files to success\nsetUploadingFiles(prev=>prev.map(uf=>({...uf,progress:100,status:'success'})));// Add to uploaded files list\nsetUploadedFiles(prev=>[...prev,...uploadedFileResults]);onFilesUploaded===null||onFilesUploaded===void 0?void 0:onFilesUploaded(uploadedFileResults);// Clear uploading files after a delay\nsetTimeout(()=>{setUploadingFiles([]);},2000);}catch(error){// Update uploading files to error\nsetUploadingFiles(prev=>prev.map(uf=>({...uf,status:'error',error:error.message||'Upload failed'})));onError===null||onError===void 0?void 0:onError(error.message||'Failed to upload files');}finally{setLoading(false);}};const handleDeleteFile=async()=>{if(!fileToDelete||!orderId)return;try{await printingService.deleteFile(orderId,fileToDelete.id);setUploadedFiles(prev=>prev.filter(f=>f.id!==fileToDelete.id));setDeleteDialogOpen(false);setFileToDelete(null);}catch(error){onError===null||onError===void 0?void 0:onError(error.message||'Failed to delete file');}};const{getRootProps,getInputProps,isDragActive}=useDropzone({onDrop,accept:settings&&Array.isArray(settings.allowed_file_types)?{'image/*':settings.allowed_file_types.filter(type=>['png','jpg','jpeg','tiff','svg'].includes(type)).map(type=>`.${type}`),'application/pdf':settings.allowed_file_types.includes('pdf')?['.pdf']:[],'application/postscript':settings.allowed_file_types.includes('eps')?['.eps']:[],'application/illustrator':settings.allowed_file_types.includes('ai')?['.ai']:[]}:undefined,maxSize:settings?settings.max_file_size_mb*1024*1024:undefined,disabled:loading||!orderId});const getFileIcon=mimeType=>{if(mimeType.startsWith('image/'))return/*#__PURE__*/_jsx(ImageIcon,{});if(mimeType==='application/pdf')return/*#__PURE__*/_jsx(PdfIcon,{});return/*#__PURE__*/_jsx(FileIcon,{});};const getStatusIcon=file=>{if(file.dpi&&settings!==null&&settings!==void 0&&settings.enable_dpi_validation){if(file.dpi>=settings.min_dpi_requirement){return/*#__PURE__*/_jsx(CheckIcon,{color:\"success\"});}else if(file.dpi>=settings.dpi_warning_threshold){return/*#__PURE__*/_jsx(WarningIcon,{color:\"warning\"});}else{return/*#__PURE__*/_jsx(ErrorIcon,{color:\"error\"});}}return file.is_approved?/*#__PURE__*/_jsx(CheckIcon,{color:\"success\"}):/*#__PURE__*/_jsx(WarningIcon,{color:\"warning\"});};const formatFileSize=bytes=>{const units=['B','KB','MB','GB'];let size=bytes;let unitIndex=0;while(size>=1024&&unitIndex<units.length-1){size/=1024;unitIndex++;}return`${size.toFixed(1)} ${units[unitIndex]}`;};if(!settings){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"200px\",children:/*#__PURE__*/_jsx(Typography,{children:\"Loading upload settings...\"})});}return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Alert,{severity:\"info\",sx:{mb:2},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Upload Limits:\"}),\" Max \",settings.max_files_per_order,\" files,\",settings.max_file_size_mb,\"MB per file, \",settings.max_total_upload_size_mb,\"MB total. Allowed types: \",settings.allowed_file_types.join(', ').toUpperCase()]})}),/*#__PURE__*/_jsxs(Paper,{...getRootProps(),sx:{p:4,border:'2px dashed',borderColor:isDragActive?'primary.main':'grey.300',backgroundColor:isDragActive?'action.hover':'background.paper',cursor:orderId?'pointer':'not-allowed',opacity:orderId?1:0.5,textAlign:'center',mb:3,transition:'all 0.2s ease-in-out'},children:[/*#__PURE__*/_jsx(\"input\",{...getInputProps()}),/*#__PURE__*/_jsx(UploadIcon,{sx:{fontSize:48,color:'text.secondary',mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:isDragActive?'Drop files here':'Drag & drop files here'}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:2},children:\"or click to browse files\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",disabled:!orderId,children:\"Browse Files\"}),!orderId&&/*#__PURE__*/_jsx(Box,{sx:{mt:1},children:/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"error\",children:\"Please create an order first\"})})]}),uploadingFiles.length>0&&/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Uploading Files\"}),uploadingFiles.map((uploadingFile,index)=>/*#__PURE__*/_jsx(Card,{sx:{mb:1},children:/*#__PURE__*/_jsxs(CardContent,{sx:{py:2},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:2,children:[getFileIcon(uploadingFile.file.type),/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",noWrap:true,children:uploadingFile.file.name}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:formatFileSize(uploadingFile.file.size)})]}),/*#__PURE__*/_jsxs(Box,{sx:{width:100},children:[uploadingFile.status==='uploading'&&/*#__PURE__*/_jsx(LinearProgress,{variant:\"indeterminate\",sx:{height:6,borderRadius:3}}),uploadingFile.status==='success'&&/*#__PURE__*/_jsx(Chip,{icon:/*#__PURE__*/_jsx(CheckIcon,{}),label:\"Success\",color:\"success\",size:\"small\"}),uploadingFile.status==='error'&&/*#__PURE__*/_jsx(Chip,{icon:/*#__PURE__*/_jsx(ErrorIcon,{}),label:\"Error\",color:\"error\",size:\"small\"})]})]}),uploadingFile.error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mt:1},children:uploadingFile.error}),uploadingFile.warnings&&uploadingFile.warnings.length>0&&/*#__PURE__*/_jsx(Alert,{severity:\"warning\",sx:{mt:1},children:uploadingFile.warnings.join(', ')})]})},index))]}),uploadedFiles.length>0&&/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,children:[\"Uploaded Files (\",uploadedFiles.length,\"/\",settings.max_files_per_order,\")\"]}),/*#__PURE__*/_jsx(Box,{sx:{display:'grid',gridTemplateColumns:'repeat(auto-fill, minmax(300px, 1fr))',gap:2},children:uploadedFiles.map(file=>/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:1,mb:1,children:[getFileIcon(file.mime_type),/*#__PURE__*/_jsx(Tooltip,{title:getStatusIcon(file),children:getStatusIcon(file)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",noWrap:true,sx:{flex:1},children:file.original_name})]}),/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[\"Size: \",file.formatted_file_size]})}),/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[\"Type: \",file.file_type_label]})}),file.dimensions&&/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[\"Dimensions: \",file.dimensions.width,\"\\xD7\",file.dimensions.height,\"px\"]})}),file.dpi&&/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:file.dpi>=(settings.min_dpi_requirement||300)?'success.main':file.dpi>=(settings.dpi_warning_threshold||150)?'warning.main':'error.main',children:[\"DPI: \",file.dpi,\" \",file.dpi_status&&`(${file.dpi_status})`]})}),file.notes&&/*#__PURE__*/_jsx(Box,{sx:{mt:1},children:/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[\"Notes: \",file.notes]})}),/*#__PURE__*/_jsx(Box,{display:\"flex\",gap:1,mt:1,children:/*#__PURE__*/_jsx(Chip,{label:file.is_approved?'Approved':'Pending Review',color:file.is_approved?'success':'warning',size:\"small\"})})]}),/*#__PURE__*/_jsxs(CardActions,{children:[/*#__PURE__*/_jsx(Button,{size:\"small\",href:file.file_url,target:\"_blank\",rel:\"noopener noreferrer\",children:\"View\"}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",color:\"error\",onClick:()=>{setFileToDelete(file);setDeleteDialogOpen(true);},children:/*#__PURE__*/_jsx(DeleteIcon,{})})]})]},file.id))})]}),/*#__PURE__*/_jsxs(Dialog,{open:deleteDialogOpen,onClose:()=>setDeleteDialogOpen(false),children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Delete File\"}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsxs(Typography,{children:[\"Are you sure you want to delete \\\"\",fileToDelete===null||fileToDelete===void 0?void 0:fileToDelete.original_name,\"\\\"? This action cannot be undone.\"]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDeleteDialogOpen(false),children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:handleDeleteFile,color:\"error\",variant:\"contained\",children:\"Delete\"})]})]})]});};export default FileUpload;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "Typography", "Paper", "<PERSON><PERSON>", "LinearProgress", "<PERSON><PERSON>", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON><PERSON>", "CloudUpload", "UploadIcon", "Delete", "DeleteIcon", "Warning", "WarningIcon", "CheckCircle", "CheckIcon", "Error", "ErrorIcon", "Image", "ImageIcon", "PictureAsPdf", "PdfIcon", "Description", "FileIcon", "useDropzone", "printingService", "jsx", "_jsx", "jsxs", "_jsxs", "FileUpload", "_ref", "orderId", "onFilesUploaded", "onError", "settings", "setSettings", "uploadedFiles", "setUploadedFiles", "uploadingFiles", "setUploadingFiles", "loading", "setLoading", "deleteDialogOpen", "setDeleteDialogOpen", "fileToDelete", "setFileToDelete", "loadSettings", "loadUploadedFiles", "uploadSettings", "getUploadSettings", "Array", "isArray", "allowed_file_types", "error", "console", "max_file_size_mb", "max_total_upload_size_mb", "max_files_per_order", "min_dpi_requirement", "dpi_warning_threshold", "enable_dpi_validation", "min_width_px", "min_height_px", "max_width_px", "max_height_px", "enable_dimension_validation", "files", "getOrderFiles", "validateFiles", "valid", "invalid", "totalFiles", "length", "excess", "i", "push", "file", "errors", "slice", "currentTotalSize", "reduce", "sum", "file_size", "newFilesSize", "size", "totalSize", "maxTotalSize", "map", "validation", "validateFile", "message", "onDrop", "acceptedFiles", "errorMessages", "_ref2", "name", "join", "uploadFiles", "newUploadingFiles", "progress", "status", "uploadedFileResults", "prev", "uf", "setTimeout", "handleDeleteFile", "deleteFile", "id", "filter", "f", "getRootProps", "getInputProps", "isDragActive", "accept", "type", "includes", "undefined", "maxSize", "disabled", "getFileIcon", "mimeType", "startsWith", "getStatusIcon", "dpi", "color", "is_approved", "formatFileSize", "bytes", "units", "unitIndex", "toFixed", "display", "justifyContent", "alignItems", "minHeight", "children", "severity", "sx", "mb", "variant", "toUpperCase", "p", "border", "borderColor", "backgroundColor", "cursor", "opacity", "textAlign", "transition", "fontSize", "gutterBottom", "mt", "uploadingFile", "index", "py", "gap", "flex", "noWrap", "width", "height", "borderRadius", "icon", "label", "warnings", "gridTemplateColumns", "mime_type", "title", "original_name", "formatted_file_size", "file_type_label", "dimensions", "dpi_status", "notes", "href", "file_url", "target", "rel", "onClick", "open", "onClose"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/FileUpload.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  LinearProgress,\n  Alert,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Card,\n  CardContent,\n  CardActions,\n  Tooltip,\n} from '@mui/material';\nimport {\n  CloudUpload as UploadIcon,\n  Delete as DeleteIcon,\n  Warning as WarningIcon,\n  CheckCircle as CheckIcon,\n  Error as ErrorIcon,\n  Image as ImageIcon,\n  PictureAsPdf as PdfIcon,\n  Description as FileIcon,\n} from '@mui/icons-material';\nimport { useDropzone } from 'react-dropzone';\nimport printingService, { OrderFile } from '../../services/printingService';\n\ninterface FileUploadSettings {\n  allowed_file_types: string[];\n  max_file_size_mb: number;\n  max_total_upload_size_mb: number;\n  max_files_per_order: number;\n  min_dpi_requirement: number;\n  dpi_warning_threshold: number;\n  enable_dpi_validation: boolean;\n  min_width_px: number;\n  min_height_px: number;\n  max_width_px: number;\n  max_height_px: number;\n  enable_dimension_validation: boolean;\n}\n\ninterface FileUploadProps {\n  orderId?: number;\n  onFilesUploaded?: (files: OrderFile[]) => void;\n  onError?: (error: string) => void;\n}\n\ninterface UploadingFile {\n  file: File;\n  progress: number;\n  status: 'uploading' | 'success' | 'error';\n  error?: string;\n  warnings?: string[];\n}\n\nconst FileUpload: React.FC<FileUploadProps> = ({ orderId, onFilesUploaded, onError }) => {\n  const [settings, setSettings] = useState<FileUploadSettings | null>(null);\n  const [uploadedFiles, setUploadedFiles] = useState<OrderFile[]>([]);\n  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [fileToDelete, setFileToDelete] = useState<OrderFile | null>(null);\n\n  // Load settings and files on component mount\n  useEffect(() => {\n    loadSettings();\n    if (orderId) {\n      loadUploadedFiles();\n    }\n  }, [orderId]);\n\n  const loadSettings = async () => {\n    try {\n      const uploadSettings = await printingService.getUploadSettings();\n\n      // Ensure allowed_file_types is always an array\n      if (uploadSettings && !Array.isArray(uploadSettings.allowed_file_types)) {\n        uploadSettings.allowed_file_types = ['pdf', 'png', 'jpg', 'jpeg'];\n      }\n\n      setSettings(uploadSettings);\n    } catch (error) {\n      console.error('Failed to load upload settings:', error);\n      onError?.('Failed to load upload settings');\n\n      // Set default settings if loading fails\n      setSettings({\n        allowed_file_types: ['pdf', 'png', 'jpg', 'jpeg'],\n        max_file_size_mb: 50,\n        max_total_upload_size_mb: 200,\n        max_files_per_order: 10,\n        min_dpi_requirement: 300,\n        dpi_warning_threshold: 150,\n        enable_dpi_validation: true,\n        min_width_px: 100,\n        min_height_px: 100,\n        max_width_px: 10000,\n        max_height_px: 10000,\n        enable_dimension_validation: true,\n      });\n    }\n  };\n\n  const loadUploadedFiles = async () => {\n    if (!orderId) return;\n\n    try {\n      const files = await printingService.getOrderFiles(orderId);\n      setUploadedFiles(files);\n    } catch (error) {\n      console.error('Failed to load uploaded files:', error);\n    }\n  };\n\n  // Validate files before upload\n  const validateFiles = async (files: File[]): Promise<{ valid: File[]; invalid: { file: File; errors: string[] }[] }> => {\n    if (!settings) return { valid: [], invalid: [] };\n\n    const valid: File[] = [];\n    const invalid: { file: File; errors: string[] }[] = [];\n\n    // Check total file count\n    const totalFiles = uploadedFiles.length + files.length;\n    if (totalFiles > settings.max_files_per_order) {\n      const excess = totalFiles - settings.max_files_per_order;\n      for (let i = files.length - excess; i < files.length; i++) {\n        invalid.push({\n          file: files[i],\n          errors: [`Maximum ${settings.max_files_per_order} files allowed per order`]\n        });\n      }\n      files = files.slice(0, files.length - excess);\n    }\n\n    // Check total upload size\n    const currentTotalSize = uploadedFiles.reduce((sum, file) => sum + file.file_size, 0);\n    const newFilesSize = files.reduce((sum, file) => sum + file.size, 0);\n    const totalSize = currentTotalSize + newFilesSize;\n    const maxTotalSize = settings.max_total_upload_size_mb * 1024 * 1024;\n\n    if (totalSize > maxTotalSize) {\n      onError?.(`Total upload size would exceed ${settings.max_total_upload_size_mb}MB limit`);\n      return { valid: [], invalid: files.map(file => ({ file, errors: ['Total size limit exceeded'] })) };\n    }\n\n    // Validate each file\n    for (const file of files) {\n      const validation = await printingService.validateFile(file);\n      if (validation.valid) {\n        valid.push(file);\n      } else {\n        invalid.push({\n          file,\n          errors: validation.message ? [validation.message] : ['Validation failed']\n        });\n      }\n    }\n\n    return { valid, invalid };\n  };\n\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    if (!orderId) {\n      onError?.('Please create an order first before uploading files');\n      return;\n    }\n\n    const { valid, invalid } = await validateFiles(acceptedFiles);\n\n    // Show errors for invalid files\n    if (invalid.length > 0) {\n      const errorMessages = invalid.map(({ file, errors }) =>\n        `${file.name}: ${errors.join(', ')}`\n      ).join('\\n');\n      onError?.(errorMessages);\n    }\n\n    // Upload valid files\n    if (valid.length > 0) {\n      await uploadFiles(valid);\n    }\n  }, [orderId, settings, uploadedFiles]);\n\n  const uploadFiles = async (files: File[]) => {\n    setLoading(true);\n\n    // Initialize uploading files state\n    const newUploadingFiles: UploadingFile[] = files.map(file => ({\n      file,\n      progress: 0,\n      status: 'uploading'\n    }));\n    setUploadingFiles(newUploadingFiles);\n\n    try {\n      const uploadedFileResults = await printingService.uploadFiles(orderId!, files, 'artwork');\n\n      // Update uploading files to success\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        progress: 100,\n        status: 'success'\n      })));\n\n      // Add to uploaded files list\n      setUploadedFiles(prev => [...prev, ...uploadedFileResults]);\n      onFilesUploaded?.(uploadedFileResults);\n\n      // Clear uploading files after a delay\n      setTimeout(() => {\n        setUploadingFiles([]);\n      }, 2000);\n\n    } catch (error: any) {\n      // Update uploading files to error\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        status: 'error',\n        error: error.message || 'Upload failed'\n      })));\n\n      onError?.(error.message || 'Failed to upload files');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteFile = async () => {\n    if (!fileToDelete || !orderId) return;\n\n    try {\n      await printingService.deleteFile(orderId, fileToDelete.id);\n      setUploadedFiles(prev => prev.filter(f => f.id !== fileToDelete.id));\n      setDeleteDialogOpen(false);\n      setFileToDelete(null);\n    } catch (error: any) {\n      onError?.(error.message || 'Failed to delete file');\n    }\n  };\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: settings && Array.isArray(settings.allowed_file_types) ? {\n      'image/*': settings.allowed_file_types.filter(type =>\n        ['png', 'jpg', 'jpeg', 'tiff', 'svg'].includes(type)\n      ).map(type => `.${type}`),\n      'application/pdf': settings.allowed_file_types.includes('pdf') ? ['.pdf'] : [],\n      'application/postscript': settings.allowed_file_types.includes('eps') ? ['.eps'] : [],\n      'application/illustrator': settings.allowed_file_types.includes('ai') ? ['.ai'] : [],\n    } : undefined,\n    maxSize: settings ? settings.max_file_size_mb * 1024 * 1024 : undefined,\n    disabled: loading || !orderId\n  });\n\n  const getFileIcon = (mimeType: string) => {\n    if (mimeType.startsWith('image/')) return <ImageIcon />;\n    if (mimeType === 'application/pdf') return <PdfIcon />;\n    return <FileIcon />;\n  };\n\n  const getStatusIcon = (file: OrderFile) => {\n    if (file.dpi && settings?.enable_dpi_validation) {\n      if (file.dpi >= settings.min_dpi_requirement) {\n        return <CheckIcon color=\"success\" />;\n      } else if (file.dpi >= settings.dpi_warning_threshold) {\n        return <WarningIcon color=\"warning\" />;\n      } else {\n        return <ErrorIcon color=\"error\" />;\n      }\n    }\n    return file.is_approved ? <CheckIcon color=\"success\" /> : <WarningIcon color=\"warning\" />;\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let size = bytes;\n    let unitIndex = 0;\n\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n\n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n  };\n\n  if (!settings) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"200px\">\n        <Typography>Loading upload settings...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Upload Settings Info */}\n      <Alert severity=\"info\" sx={{ mb: 2 }}>\n        <Typography variant=\"body2\">\n          <strong>Upload Limits:</strong> Max {settings.max_files_per_order} files,\n          {settings.max_file_size_mb}MB per file, {settings.max_total_upload_size_mb}MB total.\n          Allowed types: {settings.allowed_file_types.join(', ').toUpperCase()}\n        </Typography>\n      </Alert>\n\n\n\n      {/* Drag and Drop Upload Area */}\n      <Paper\n        {...getRootProps()}\n        sx={{\n          p: 4,\n          border: '2px dashed',\n          borderColor: isDragActive ? 'primary.main' : 'grey.300',\n          backgroundColor: isDragActive ? 'action.hover' : 'background.paper',\n          cursor: orderId ? 'pointer' : 'not-allowed',\n          opacity: orderId ? 1 : 0.5,\n          textAlign: 'center',\n          mb: 3,\n          transition: 'all 0.2s ease-in-out',\n        }}\n      >\n        <input {...getInputProps()} />\n        <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />\n        <Typography variant=\"h6\" gutterBottom>\n          {isDragActive ? 'Drop files here' : 'Drag & drop files here'}\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          or click to browse files\n        </Typography>\n        <Button variant=\"outlined\" disabled={!orderId}>\n          Browse Files\n        </Button>\n        {!orderId && (\n          <Box sx={{ mt: 1 }}>\n            <Typography variant=\"caption\" color=\"error\">\n              Please create an order first\n            </Typography>\n          </Box>\n        )}\n      </Paper>\n\n      {/* Uploading Files */}\n      {uploadingFiles.length > 0 && (\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Uploading Files\n          </Typography>\n          {uploadingFiles.map((uploadingFile, index) => (\n            <Card key={index} sx={{ mb: 1 }}>\n              <CardContent sx={{ py: 2 }}>\n                <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                  {getFileIcon(uploadingFile.file.type)}\n                  <Box sx={{ flex: 1 }}>\n                    <Typography variant=\"body2\" noWrap>\n                      {uploadingFile.file.name}\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      {formatFileSize(uploadingFile.file.size)}\n                    </Typography>\n                  </Box>\n                  <Box sx={{ width: 100 }}>\n                    {uploadingFile.status === 'uploading' && (\n                      <LinearProgress\n                        variant=\"indeterminate\"\n                        sx={{ height: 6, borderRadius: 3 }}\n                      />\n                    )}\n                    {uploadingFile.status === 'success' && (\n                      <Chip\n                        icon={<CheckIcon />}\n                        label=\"Success\"\n                        color=\"success\"\n                        size=\"small\"\n                      />\n                    )}\n                    {uploadingFile.status === 'error' && (\n                      <Chip\n                        icon={<ErrorIcon />}\n                        label=\"Error\"\n                        color=\"error\"\n                        size=\"small\"\n                      />\n                    )}\n                  </Box>\n                </Box>\n                {uploadingFile.error && (\n                  <Alert severity=\"error\" sx={{ mt: 1 }}>\n                    {uploadingFile.error}\n                  </Alert>\n                )}\n                {uploadingFile.warnings && uploadingFile.warnings.length > 0 && (\n                  <Alert severity=\"warning\" sx={{ mt: 1 }}>\n                    {uploadingFile.warnings.join(', ')}\n                  </Alert>\n                )}\n              </CardContent>\n            </Card>\n          ))}\n        </Box>\n      )}\n\n      {/* Uploaded Files List */}\n      {uploadedFiles.length > 0 && (\n        <Box>\n          <Typography variant=\"h6\" gutterBottom>\n            Uploaded Files ({uploadedFiles.length}/{settings.max_files_per_order})\n          </Typography>\n          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: 2 }}>\n            {uploadedFiles.map((file) => (\n              <Card key={file.id}>\n                <CardContent>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1} mb={1}>\n                    {getFileIcon(file.mime_type)}\n                    <Tooltip title={getStatusIcon(file)}>\n                      {getStatusIcon(file)}\n                    </Tooltip>\n                    <Typography variant=\"body2\" noWrap sx={{ flex: 1 }}>\n                      {file.original_name}\n                    </Typography>\n                  </Box>\n\n                  <Box>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Size: {file.formatted_file_size}\n                    </Typography>\n                  </Box>\n\n                  <Box>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Type: {file.file_type_label}\n                    </Typography>\n                  </Box>\n\n                  {file.dimensions && (\n                    <Box>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Dimensions: {file.dimensions.width}×{file.dimensions.height}px\n                      </Typography>\n                    </Box>\n                  )}\n\n                  {file.dpi && (\n                    <Box>\n                      <Typography\n                        variant=\"caption\"\n                        color={\n                          file.dpi >= (settings.min_dpi_requirement || 300) ? 'success.main' :\n                          file.dpi >= (settings.dpi_warning_threshold || 150) ? 'warning.main' : 'error.main'\n                        }\n                      >\n                        DPI: {file.dpi} {file.dpi_status && `(${file.dpi_status})`}\n                      </Typography>\n                    </Box>\n                  )}\n\n                  {file.notes && (\n                    <Box sx={{ mt: 1 }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Notes: {file.notes}\n                      </Typography>\n                    </Box>\n                  )}\n\n                  <Box display=\"flex\" gap={1} mt={1}>\n                    <Chip\n                      label={file.is_approved ? 'Approved' : 'Pending Review'}\n                      color={file.is_approved ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  </Box>\n                </CardContent>\n\n                <CardActions>\n                  <Button\n                    size=\"small\"\n                    href={file.file_url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    View\n                  </Button>\n                  <IconButton\n                    size=\"small\"\n                    color=\"error\"\n                    onClick={() => {\n                      setFileToDelete(file);\n                      setDeleteDialogOpen(true);\n                    }}\n                  >\n                    <DeleteIcon />\n                  </IconButton>\n                </CardActions>\n              </Card>\n            ))}\n          </Box>\n        </Box>\n      )}\n\n      {/* Delete Confirmation Dialog */}\n      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>\n        <DialogTitle>Delete File</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Are you sure you want to delete \"{fileToDelete?.original_name}\"? This action cannot be undone.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>\n          <Button onClick={handleDeleteFile} color=\"error\" variant=\"contained\">\n            Delete\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default FileUpload;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,cAAc,CACdC,KAAK,CACLC,IAAI,CACJC,UAAU,CACVC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,OAAO,KACF,eAAe,CACtB,OACEC,WAAW,GAAI,CAAAC,UAAU,CACzBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,WAAW,GAAI,CAAAC,SAAS,CACxBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,YAAY,GAAI,CAAAC,OAAO,CACvBC,WAAW,GAAI,CAAAC,QAAQ,KAClB,qBAAqB,CAC5B,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,MAAO,CAAAC,eAAe,KAAqB,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA+B5E,KAAM,CAAAC,UAAqC,CAAGC,IAAA,EAA2C,IAA1C,CAAEC,OAAO,CAAEC,eAAe,CAAEC,OAAQ,CAAC,CAAAH,IAAA,CAClF,KAAM,CAACI,QAAQ,CAAEC,WAAW,CAAC,CAAG/C,QAAQ,CAA4B,IAAI,CAAC,CACzE,KAAM,CAACgD,aAAa,CAAEC,gBAAgB,CAAC,CAAGjD,QAAQ,CAAc,EAAE,CAAC,CACnE,KAAM,CAACkD,cAAc,CAAEC,iBAAiB,CAAC,CAAGnD,QAAQ,CAAkB,EAAE,CAAC,CACzE,KAAM,CAACoD,OAAO,CAAEC,UAAU,CAAC,CAAGrD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACsD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvD,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACwD,YAAY,CAAEC,eAAe,CAAC,CAAGzD,QAAQ,CAAmB,IAAI,CAAC,CAExE;AACAC,SAAS,CAAC,IAAM,CACdyD,YAAY,CAAC,CAAC,CACd,GAAIf,OAAO,CAAE,CACXgB,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAC,CAAE,CAAChB,OAAO,CAAC,CAAC,CAEb,KAAM,CAAAe,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAE,cAAc,CAAG,KAAM,CAAAxB,eAAe,CAACyB,iBAAiB,CAAC,CAAC,CAEhE;AACA,GAAID,cAAc,EAAI,CAACE,KAAK,CAACC,OAAO,CAACH,cAAc,CAACI,kBAAkB,CAAC,CAAE,CACvEJ,cAAc,CAACI,kBAAkB,CAAG,CAAC,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,MAAM,CAAC,CACnE,CAEAjB,WAAW,CAACa,cAAc,CAAC,CAC7B,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDpB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAG,gCAAgC,CAAC,CAE3C;AACAE,WAAW,CAAC,CACViB,kBAAkB,CAAE,CAAC,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,MAAM,CAAC,CACjDG,gBAAgB,CAAE,EAAE,CACpBC,wBAAwB,CAAE,GAAG,CAC7BC,mBAAmB,CAAE,EAAE,CACvBC,mBAAmB,CAAE,GAAG,CACxBC,qBAAqB,CAAE,GAAG,CAC1BC,qBAAqB,CAAE,IAAI,CAC3BC,YAAY,CAAE,GAAG,CACjBC,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,KAAK,CACnBC,aAAa,CAAE,KAAK,CACpBC,2BAA2B,CAAE,IAC/B,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAlB,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAAChB,OAAO,CAAE,OAEd,GAAI,CACF,KAAM,CAAAmC,KAAK,CAAG,KAAM,CAAA1C,eAAe,CAAC2C,aAAa,CAACpC,OAAO,CAAC,CAC1DM,gBAAgB,CAAC6B,KAAK,CAAC,CACzB,CAAE,MAAOb,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACxD,CACF,CAAC,CAED;AACA,KAAM,CAAAe,aAAa,CAAG,KAAO,CAAAF,KAAa,EAA8E,CACtH,GAAI,CAAChC,QAAQ,CAAE,MAAO,CAAEmC,KAAK,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAC,CAEhD,KAAM,CAAAD,KAAa,CAAG,EAAE,CACxB,KAAM,CAAAC,OAA2C,CAAG,EAAE,CAEtD;AACA,KAAM,CAAAC,UAAU,CAAGnC,aAAa,CAACoC,MAAM,CAAGN,KAAK,CAACM,MAAM,CACtD,GAAID,UAAU,CAAGrC,QAAQ,CAACuB,mBAAmB,CAAE,CAC7C,KAAM,CAAAgB,MAAM,CAAGF,UAAU,CAAGrC,QAAQ,CAACuB,mBAAmB,CACxD,IAAK,GAAI,CAAAiB,CAAC,CAAGR,KAAK,CAACM,MAAM,CAAGC,MAAM,CAAEC,CAAC,CAAGR,KAAK,CAACM,MAAM,CAAEE,CAAC,EAAE,CAAE,CACzDJ,OAAO,CAACK,IAAI,CAAC,CACXC,IAAI,CAAEV,KAAK,CAACQ,CAAC,CAAC,CACdG,MAAM,CAAE,CAAC,WAAW3C,QAAQ,CAACuB,mBAAmB,0BAA0B,CAC5E,CAAC,CAAC,CACJ,CACAS,KAAK,CAAGA,KAAK,CAACY,KAAK,CAAC,CAAC,CAAEZ,KAAK,CAACM,MAAM,CAAGC,MAAM,CAAC,CAC/C,CAEA;AACA,KAAM,CAAAM,gBAAgB,CAAG3C,aAAa,CAAC4C,MAAM,CAAC,CAACC,GAAG,CAAEL,IAAI,GAAKK,GAAG,CAAGL,IAAI,CAACM,SAAS,CAAE,CAAC,CAAC,CACrF,KAAM,CAAAC,YAAY,CAAGjB,KAAK,CAACc,MAAM,CAAC,CAACC,GAAG,CAAEL,IAAI,GAAKK,GAAG,CAAGL,IAAI,CAACQ,IAAI,CAAE,CAAC,CAAC,CACpE,KAAM,CAAAC,SAAS,CAAGN,gBAAgB,CAAGI,YAAY,CACjD,KAAM,CAAAG,YAAY,CAAGpD,QAAQ,CAACsB,wBAAwB,CAAG,IAAI,CAAG,IAAI,CAEpE,GAAI6B,SAAS,CAAGC,YAAY,CAAE,CAC5BrD,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAG,kCAAkCC,QAAQ,CAACsB,wBAAwB,UAAU,CAAC,CACxF,MAAO,CAAEa,KAAK,CAAE,EAAE,CAAEC,OAAO,CAAEJ,KAAK,CAACqB,GAAG,CAACX,IAAI,GAAK,CAAEA,IAAI,CAAEC,MAAM,CAAE,CAAC,2BAA2B,CAAE,CAAC,CAAC,CAAE,CAAC,CACrG,CAEA;AACA,IAAK,KAAM,CAAAD,IAAI,GAAI,CAAAV,KAAK,CAAE,CACxB,KAAM,CAAAsB,UAAU,CAAG,KAAM,CAAAhE,eAAe,CAACiE,YAAY,CAACb,IAAI,CAAC,CAC3D,GAAIY,UAAU,CAACnB,KAAK,CAAE,CACpBA,KAAK,CAACM,IAAI,CAACC,IAAI,CAAC,CAClB,CAAC,IAAM,CACLN,OAAO,CAACK,IAAI,CAAC,CACXC,IAAI,CACJC,MAAM,CAAEW,UAAU,CAACE,OAAO,CAAG,CAACF,UAAU,CAACE,OAAO,CAAC,CAAG,CAAC,mBAAmB,CAC1E,CAAC,CAAC,CACJ,CACF,CAEA,MAAO,CAAErB,KAAK,CAAEC,OAAQ,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAqB,MAAM,CAAGrG,WAAW,CAAC,KAAO,CAAAsG,aAAqB,EAAK,CAC1D,GAAI,CAAC7D,OAAO,CAAE,CACZE,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAG,qDAAqD,CAAC,CAChE,OACF,CAEA,KAAM,CAAEoC,KAAK,CAAEC,OAAQ,CAAC,CAAG,KAAM,CAAAF,aAAa,CAACwB,aAAa,CAAC,CAE7D;AACA,GAAItB,OAAO,CAACE,MAAM,CAAG,CAAC,CAAE,CACtB,KAAM,CAAAqB,aAAa,CAAGvB,OAAO,CAACiB,GAAG,CAACO,KAAA,MAAC,CAAElB,IAAI,CAAEC,MAAO,CAAC,CAAAiB,KAAA,OACjD,GAAGlB,IAAI,CAACmB,IAAI,KAAKlB,MAAM,CAACmB,IAAI,CAAC,IAAI,CAAC,EAAE,EACtC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CACZ/D,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAG4D,aAAa,CAAC,CAC1B,CAEA;AACA,GAAIxB,KAAK,CAACG,MAAM,CAAG,CAAC,CAAE,CACpB,KAAM,CAAAyB,WAAW,CAAC5B,KAAK,CAAC,CAC1B,CACF,CAAC,CAAE,CAACtC,OAAO,CAAEG,QAAQ,CAAEE,aAAa,CAAC,CAAC,CAEtC,KAAM,CAAA6D,WAAW,CAAG,KAAO,CAAA/B,KAAa,EAAK,CAC3CzB,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAAAyD,iBAAkC,CAAGhC,KAAK,CAACqB,GAAG,CAACX,IAAI,GAAK,CAC5DA,IAAI,CACJuB,QAAQ,CAAE,CAAC,CACXC,MAAM,CAAE,WACV,CAAC,CAAC,CAAC,CACH7D,iBAAiB,CAAC2D,iBAAiB,CAAC,CAEpC,GAAI,CACF,KAAM,CAAAG,mBAAmB,CAAG,KAAM,CAAA7E,eAAe,CAACyE,WAAW,CAAClE,OAAO,CAAGmC,KAAK,CAAE,SAAS,CAAC,CAEzF;AACA3B,iBAAiB,CAAC+D,IAAI,EAAIA,IAAI,CAACf,GAAG,CAACgB,EAAE,GAAK,CACxC,GAAGA,EAAE,CACLJ,QAAQ,CAAE,GAAG,CACbC,MAAM,CAAE,SACV,CAAC,CAAC,CAAC,CAAC,CAEJ;AACA/D,gBAAgB,CAACiE,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,GAAGD,mBAAmB,CAAC,CAAC,CAC3DrE,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAGqE,mBAAmB,CAAC,CAEtC;AACAG,UAAU,CAAC,IAAM,CACfjE,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAC,CAAE,IAAI,CAAC,CAEV,CAAE,MAAOc,KAAU,CAAE,CACnB;AACAd,iBAAiB,CAAC+D,IAAI,EAAIA,IAAI,CAACf,GAAG,CAACgB,EAAE,GAAK,CACxC,GAAGA,EAAE,CACLH,MAAM,CAAE,OAAO,CACf/C,KAAK,CAAEA,KAAK,CAACqC,OAAO,EAAI,eAC1B,CAAC,CAAC,CAAC,CAAC,CAEJzD,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAGoB,KAAK,CAACqC,OAAO,EAAI,wBAAwB,CAAC,CACtD,CAAC,OAAS,CACRjD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAgE,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CAAC7D,YAAY,EAAI,CAACb,OAAO,CAAE,OAE/B,GAAI,CACF,KAAM,CAAAP,eAAe,CAACkF,UAAU,CAAC3E,OAAO,CAAEa,YAAY,CAAC+D,EAAE,CAAC,CAC1DtE,gBAAgB,CAACiE,IAAI,EAAIA,IAAI,CAACM,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACF,EAAE,GAAK/D,YAAY,CAAC+D,EAAE,CAAC,CAAC,CACpEhE,mBAAmB,CAAC,KAAK,CAAC,CAC1BE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,MAAOQ,KAAU,CAAE,CACnBpB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAGoB,KAAK,CAACqC,OAAO,EAAI,uBAAuB,CAAC,CACrD,CACF,CAAC,CAED,KAAM,CAAEoB,YAAY,CAAEC,aAAa,CAAEC,YAAa,CAAC,CAAGzF,WAAW,CAAC,CAChEoE,MAAM,CACNsB,MAAM,CAAE/E,QAAQ,EAAIgB,KAAK,CAACC,OAAO,CAACjB,QAAQ,CAACkB,kBAAkB,CAAC,CAAG,CAC/D,SAAS,CAAElB,QAAQ,CAACkB,kBAAkB,CAACwD,MAAM,CAACM,IAAI,EAChD,CAAC,KAAK,CAAE,KAAK,CAAE,MAAM,CAAE,MAAM,CAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,IAAI,CACrD,CAAC,CAAC3B,GAAG,CAAC2B,IAAI,EAAI,IAAIA,IAAI,EAAE,CAAC,CACzB,iBAAiB,CAAEhF,QAAQ,CAACkB,kBAAkB,CAAC+D,QAAQ,CAAC,KAAK,CAAC,CAAG,CAAC,MAAM,CAAC,CAAG,EAAE,CAC9E,wBAAwB,CAAEjF,QAAQ,CAACkB,kBAAkB,CAAC+D,QAAQ,CAAC,KAAK,CAAC,CAAG,CAAC,MAAM,CAAC,CAAG,EAAE,CACrF,yBAAyB,CAAEjF,QAAQ,CAACkB,kBAAkB,CAAC+D,QAAQ,CAAC,IAAI,CAAC,CAAG,CAAC,KAAK,CAAC,CAAG,EACpF,CAAC,CAAGC,SAAS,CACbC,OAAO,CAAEnF,QAAQ,CAAGA,QAAQ,CAACqB,gBAAgB,CAAG,IAAI,CAAG,IAAI,CAAG6D,SAAS,CACvEE,QAAQ,CAAE9E,OAAO,EAAI,CAACT,OACxB,CAAC,CAAC,CAEF,KAAM,CAAAwF,WAAW,CAAIC,QAAgB,EAAK,CACxC,GAAIA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAE,mBAAO/F,IAAA,CAACR,SAAS,GAAE,CAAC,CACvD,GAAIsG,QAAQ,GAAK,iBAAiB,CAAE,mBAAO9F,IAAA,CAACN,OAAO,GAAE,CAAC,CACtD,mBAAOM,IAAA,CAACJ,QAAQ,GAAE,CAAC,CACrB,CAAC,CAED,KAAM,CAAAoG,aAAa,CAAI9C,IAAe,EAAK,CACzC,GAAIA,IAAI,CAAC+C,GAAG,EAAIzF,QAAQ,SAARA,QAAQ,WAARA,QAAQ,CAAE0B,qBAAqB,CAAE,CAC/C,GAAIgB,IAAI,CAAC+C,GAAG,EAAIzF,QAAQ,CAACwB,mBAAmB,CAAE,CAC5C,mBAAOhC,IAAA,CAACZ,SAAS,EAAC8G,KAAK,CAAC,SAAS,CAAE,CAAC,CACtC,CAAC,IAAM,IAAIhD,IAAI,CAAC+C,GAAG,EAAIzF,QAAQ,CAACyB,qBAAqB,CAAE,CACrD,mBAAOjC,IAAA,CAACd,WAAW,EAACgH,KAAK,CAAC,SAAS,CAAE,CAAC,CACxC,CAAC,IAAM,CACL,mBAAOlG,IAAA,CAACV,SAAS,EAAC4G,KAAK,CAAC,OAAO,CAAE,CAAC,CACpC,CACF,CACA,MAAO,CAAAhD,IAAI,CAACiD,WAAW,cAAGnG,IAAA,CAACZ,SAAS,EAAC8G,KAAK,CAAC,SAAS,CAAE,CAAC,cAAGlG,IAAA,CAACd,WAAW,EAACgH,KAAK,CAAC,SAAS,CAAE,CAAC,CAC3F,CAAC,CAED,KAAM,CAAAE,cAAc,CAAIC,KAAa,EAAa,CAChD,KAAM,CAAAC,KAAK,CAAG,CAAC,GAAG,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CACrC,GAAI,CAAA5C,IAAI,CAAG2C,KAAK,CAChB,GAAI,CAAAE,SAAS,CAAG,CAAC,CAEjB,MAAO7C,IAAI,EAAI,IAAI,EAAI6C,SAAS,CAAGD,KAAK,CAACxD,MAAM,CAAG,CAAC,CAAE,CACnDY,IAAI,EAAI,IAAI,CACZ6C,SAAS,EAAE,CACb,CAEA,MAAO,GAAG7C,IAAI,CAAC8C,OAAO,CAAC,CAAC,CAAC,IAAIF,KAAK,CAACC,SAAS,CAAC,EAAE,CACjD,CAAC,CAED,GAAI,CAAC/F,QAAQ,CAAE,CACb,mBACER,IAAA,CAACnC,GAAG,EAAC4I,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAACC,SAAS,CAAC,OAAO,CAAAC,QAAA,cAC/E7G,IAAA,CAAClC,UAAU,EAAA+I,QAAA,CAAC,4BAA0B,CAAY,CAAC,CAChD,CAAC,CAEV,CAEA,mBACE3G,KAAA,CAACrC,GAAG,EAAAgJ,QAAA,eAEF7G,IAAA,CAAC9B,KAAK,EAAC4I,QAAQ,CAAC,MAAM,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,cACnC3G,KAAA,CAACpC,UAAU,EAACmJ,OAAO,CAAC,OAAO,CAAAJ,QAAA,eACzB7G,IAAA,WAAA6G,QAAA,CAAQ,gBAAc,CAAQ,CAAC,QAAK,CAACrG,QAAQ,CAACuB,mBAAmB,CAAC,SAClE,CAACvB,QAAQ,CAACqB,gBAAgB,CAAC,eAAa,CAACrB,QAAQ,CAACsB,wBAAwB,CAAC,2BAC5D,CAACtB,QAAQ,CAACkB,kBAAkB,CAAC4C,IAAI,CAAC,IAAI,CAAC,CAAC4C,WAAW,CAAC,CAAC,EAC1D,CAAC,CACR,CAAC,cAKRhH,KAAA,CAACnC,KAAK,KACAqH,YAAY,CAAC,CAAC,CAClB2B,EAAE,CAAE,CACFI,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,YAAY,CACpBC,WAAW,CAAE/B,YAAY,CAAG,cAAc,CAAG,UAAU,CACvDgC,eAAe,CAAEhC,YAAY,CAAG,cAAc,CAAG,kBAAkB,CACnEiC,MAAM,CAAElH,OAAO,CAAG,SAAS,CAAG,aAAa,CAC3CmH,OAAO,CAAEnH,OAAO,CAAG,CAAC,CAAG,GAAG,CAC1BoH,SAAS,CAAE,QAAQ,CACnBT,EAAE,CAAE,CAAC,CACLU,UAAU,CAAE,sBACd,CAAE,CAAAb,QAAA,eAEF7G,IAAA,aAAWqF,aAAa,CAAC,CAAC,CAAG,CAAC,cAC9BrF,IAAA,CAAClB,UAAU,EAACiI,EAAE,CAAE,CAAEY,QAAQ,CAAE,EAAE,CAAEzB,KAAK,CAAE,gBAAgB,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cACpEhH,IAAA,CAAClC,UAAU,EAACmJ,OAAO,CAAC,IAAI,CAACW,YAAY,MAAAf,QAAA,CAClCvB,YAAY,CAAG,iBAAiB,CAAG,wBAAwB,CAClD,CAAC,cACbtF,IAAA,CAAClC,UAAU,EAACmJ,OAAO,CAAC,OAAO,CAACf,KAAK,CAAC,gBAAgB,CAACa,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CAAC,0BAElE,CAAY,CAAC,cACb7G,IAAA,CAAChC,MAAM,EAACiJ,OAAO,CAAC,UAAU,CAACrB,QAAQ,CAAE,CAACvF,OAAQ,CAAAwG,QAAA,CAAC,cAE/C,CAAQ,CAAC,CACR,CAACxG,OAAO,eACPL,IAAA,CAACnC,GAAG,EAACkJ,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAAhB,QAAA,cACjB7G,IAAA,CAAClC,UAAU,EAACmJ,OAAO,CAAC,SAAS,CAACf,KAAK,CAAC,OAAO,CAAAW,QAAA,CAAC,8BAE5C,CAAY,CAAC,CACV,CACN,EACI,CAAC,CAGPjG,cAAc,CAACkC,MAAM,CAAG,CAAC,eACxB5C,KAAA,CAACrC,GAAG,EAACkJ,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjB7G,IAAA,CAAClC,UAAU,EAACmJ,OAAO,CAAC,IAAI,CAACW,YAAY,MAAAf,QAAA,CAAC,iBAEtC,CAAY,CAAC,CACZjG,cAAc,CAACiD,GAAG,CAAC,CAACiE,aAAa,CAAEC,KAAK,gBACvC/H,IAAA,CAACvB,IAAI,EAAasI,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,cAC9B3G,KAAA,CAACxB,WAAW,EAACqI,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACzB3G,KAAA,CAACrC,GAAG,EAAC4I,OAAO,CAAC,MAAM,CAACE,UAAU,CAAC,QAAQ,CAACsB,GAAG,CAAE,CAAE,CAAApB,QAAA,EAC5ChB,WAAW,CAACiC,aAAa,CAAC5E,IAAI,CAACsC,IAAI,CAAC,cACrCtF,KAAA,CAACrC,GAAG,EAACkJ,EAAE,CAAE,CAAEmB,IAAI,CAAE,CAAE,CAAE,CAAArB,QAAA,eACnB7G,IAAA,CAAClC,UAAU,EAACmJ,OAAO,CAAC,OAAO,CAACkB,MAAM,MAAAtB,QAAA,CAC/BiB,aAAa,CAAC5E,IAAI,CAACmB,IAAI,CACd,CAAC,cACbrE,IAAA,CAAClC,UAAU,EAACmJ,OAAO,CAAC,SAAS,CAACf,KAAK,CAAC,gBAAgB,CAAAW,QAAA,CACjDT,cAAc,CAAC0B,aAAa,CAAC5E,IAAI,CAACQ,IAAI,CAAC,CAC9B,CAAC,EACV,CAAC,cACNxD,KAAA,CAACrC,GAAG,EAACkJ,EAAE,CAAE,CAAEqB,KAAK,CAAE,GAAI,CAAE,CAAAvB,QAAA,EACrBiB,aAAa,CAACpD,MAAM,GAAK,WAAW,eACnC1E,IAAA,CAAC/B,cAAc,EACbgJ,OAAO,CAAC,eAAe,CACvBF,EAAE,CAAE,CAAEsB,MAAM,CAAE,CAAC,CAAEC,YAAY,CAAE,CAAE,CAAE,CACpC,CACF,CACAR,aAAa,CAACpD,MAAM,GAAK,SAAS,eACjC1E,IAAA,CAAC7B,IAAI,EACHoK,IAAI,cAAEvI,IAAA,CAACZ,SAAS,GAAE,CAAE,CACpBoJ,KAAK,CAAC,SAAS,CACftC,KAAK,CAAC,SAAS,CACfxC,IAAI,CAAC,OAAO,CACb,CACF,CACAoE,aAAa,CAACpD,MAAM,GAAK,OAAO,eAC/B1E,IAAA,CAAC7B,IAAI,EACHoK,IAAI,cAAEvI,IAAA,CAACV,SAAS,GAAE,CAAE,CACpBkJ,KAAK,CAAC,OAAO,CACbtC,KAAK,CAAC,OAAO,CACbxC,IAAI,CAAC,OAAO,CACb,CACF,EACE,CAAC,EACH,CAAC,CACLoE,aAAa,CAACnG,KAAK,eAClB3B,IAAA,CAAC9B,KAAK,EAAC4I,QAAQ,CAAC,OAAO,CAACC,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAAhB,QAAA,CACnCiB,aAAa,CAACnG,KAAK,CACf,CACR,CACAmG,aAAa,CAACW,QAAQ,EAAIX,aAAa,CAACW,QAAQ,CAAC3F,MAAM,CAAG,CAAC,eAC1D9C,IAAA,CAAC9B,KAAK,EAAC4I,QAAQ,CAAC,SAAS,CAACC,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAAhB,QAAA,CACrCiB,aAAa,CAACW,QAAQ,CAACnE,IAAI,CAAC,IAAI,CAAC,CAC7B,CACR,EACU,CAAC,EA/CLyD,KAgDL,CACP,CAAC,EACC,CACN,CAGArH,aAAa,CAACoC,MAAM,CAAG,CAAC,eACvB5C,KAAA,CAACrC,GAAG,EAAAgJ,QAAA,eACF3G,KAAA,CAACpC,UAAU,EAACmJ,OAAO,CAAC,IAAI,CAACW,YAAY,MAAAf,QAAA,EAAC,kBACpB,CAACnG,aAAa,CAACoC,MAAM,CAAC,GAAC,CAACtC,QAAQ,CAACuB,mBAAmB,CAAC,GACvE,EAAY,CAAC,cACb/B,IAAA,CAACnC,GAAG,EAACkJ,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAEiC,mBAAmB,CAAE,uCAAuC,CAAET,GAAG,CAAE,CAAE,CAAE,CAAApB,QAAA,CAChGnG,aAAa,CAACmD,GAAG,CAAEX,IAAI,eACtBhD,KAAA,CAACzB,IAAI,EAAAoI,QAAA,eACH3G,KAAA,CAACxB,WAAW,EAAAmI,QAAA,eACV3G,KAAA,CAACrC,GAAG,EAAC4I,OAAO,CAAC,MAAM,CAACE,UAAU,CAAC,QAAQ,CAACsB,GAAG,CAAE,CAAE,CAACjB,EAAE,CAAE,CAAE,CAAAH,QAAA,EACnDhB,WAAW,CAAC3C,IAAI,CAACyF,SAAS,CAAC,cAC5B3I,IAAA,CAACpB,OAAO,EAACgK,KAAK,CAAE5C,aAAa,CAAC9C,IAAI,CAAE,CAAA2D,QAAA,CACjCb,aAAa,CAAC9C,IAAI,CAAC,CACb,CAAC,cACVlD,IAAA,CAAClC,UAAU,EAACmJ,OAAO,CAAC,OAAO,CAACkB,MAAM,MAACpB,EAAE,CAAE,CAAEmB,IAAI,CAAE,CAAE,CAAE,CAAArB,QAAA,CAChD3D,IAAI,CAAC2F,aAAa,CACT,CAAC,EACV,CAAC,cAEN7I,IAAA,CAACnC,GAAG,EAAAgJ,QAAA,cACF3G,KAAA,CAACpC,UAAU,EAACmJ,OAAO,CAAC,SAAS,CAACf,KAAK,CAAC,gBAAgB,CAAAW,QAAA,EAAC,QAC7C,CAAC3D,IAAI,CAAC4F,mBAAmB,EACrB,CAAC,CACV,CAAC,cAEN9I,IAAA,CAACnC,GAAG,EAAAgJ,QAAA,cACF3G,KAAA,CAACpC,UAAU,EAACmJ,OAAO,CAAC,SAAS,CAACf,KAAK,CAAC,gBAAgB,CAAAW,QAAA,EAAC,QAC7C,CAAC3D,IAAI,CAAC6F,eAAe,EACjB,CAAC,CACV,CAAC,CAEL7F,IAAI,CAAC8F,UAAU,eACdhJ,IAAA,CAACnC,GAAG,EAAAgJ,QAAA,cACF3G,KAAA,CAACpC,UAAU,EAACmJ,OAAO,CAAC,SAAS,CAACf,KAAK,CAAC,gBAAgB,CAAAW,QAAA,EAAC,cACvC,CAAC3D,IAAI,CAAC8F,UAAU,CAACZ,KAAK,CAAC,MAAC,CAAClF,IAAI,CAAC8F,UAAU,CAACX,MAAM,CAAC,IAC9D,EAAY,CAAC,CACV,CACN,CAEAnF,IAAI,CAAC+C,GAAG,eACPjG,IAAA,CAACnC,GAAG,EAAAgJ,QAAA,cACF3G,KAAA,CAACpC,UAAU,EACTmJ,OAAO,CAAC,SAAS,CACjBf,KAAK,CACHhD,IAAI,CAAC+C,GAAG,GAAKzF,QAAQ,CAACwB,mBAAmB,EAAI,GAAG,CAAC,CAAG,cAAc,CAClEkB,IAAI,CAAC+C,GAAG,GAAKzF,QAAQ,CAACyB,qBAAqB,EAAI,GAAG,CAAC,CAAG,cAAc,CAAG,YACxE,CAAA4E,QAAA,EACF,OACM,CAAC3D,IAAI,CAAC+C,GAAG,CAAC,GAAC,CAAC/C,IAAI,CAAC+F,UAAU,EAAI,IAAI/F,IAAI,CAAC+F,UAAU,GAAG,EAChD,CAAC,CACV,CACN,CAEA/F,IAAI,CAACgG,KAAK,eACTlJ,IAAA,CAACnC,GAAG,EAACkJ,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAAhB,QAAA,cACjB3G,KAAA,CAACpC,UAAU,EAACmJ,OAAO,CAAC,SAAS,CAACf,KAAK,CAAC,gBAAgB,CAAAW,QAAA,EAAC,SAC5C,CAAC3D,IAAI,CAACgG,KAAK,EACR,CAAC,CACV,CACN,cAEDlJ,IAAA,CAACnC,GAAG,EAAC4I,OAAO,CAAC,MAAM,CAACwB,GAAG,CAAE,CAAE,CAACJ,EAAE,CAAE,CAAE,CAAAhB,QAAA,cAChC7G,IAAA,CAAC7B,IAAI,EACHqK,KAAK,CAAEtF,IAAI,CAACiD,WAAW,CAAG,UAAU,CAAG,gBAAiB,CACxDD,KAAK,CAAEhD,IAAI,CAACiD,WAAW,CAAG,SAAS,CAAG,SAAU,CAChDzC,IAAI,CAAC,OAAO,CACb,CAAC,CACC,CAAC,EACK,CAAC,cAEdxD,KAAA,CAACvB,WAAW,EAAAkI,QAAA,eACV7G,IAAA,CAAChC,MAAM,EACL0F,IAAI,CAAC,OAAO,CACZyF,IAAI,CAAEjG,IAAI,CAACkG,QAAS,CACpBC,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CAAAzC,QAAA,CAC1B,MAED,CAAQ,CAAC,cACT7G,IAAA,CAAC5B,UAAU,EACTsF,IAAI,CAAC,OAAO,CACZwC,KAAK,CAAC,OAAO,CACbqD,OAAO,CAAEA,CAAA,GAAM,CACbpI,eAAe,CAAC+B,IAAI,CAAC,CACrBjC,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAE,CAAA4F,QAAA,cAEF7G,IAAA,CAAChB,UAAU,GAAE,CAAC,CACJ,CAAC,EACF,CAAC,GAlFLkE,IAAI,CAAC+B,EAmFV,CACP,CAAC,CACC,CAAC,EACH,CACN,cAGD/E,KAAA,CAAC7B,MAAM,EAACmL,IAAI,CAAExI,gBAAiB,CAACyI,OAAO,CAAEA,CAAA,GAAMxI,mBAAmB,CAAC,KAAK,CAAE,CAAA4F,QAAA,eACxE7G,IAAA,CAAC1B,WAAW,EAAAuI,QAAA,CAAC,aAAW,CAAa,CAAC,cACtC7G,IAAA,CAACzB,aAAa,EAAAsI,QAAA,cACZ3G,KAAA,CAACpC,UAAU,EAAA+I,QAAA,EAAC,oCACuB,CAAC3F,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE2H,aAAa,CAAC,mCAChE,EAAY,CAAC,CACA,CAAC,cAChB3I,KAAA,CAAC1B,aAAa,EAAAqI,QAAA,eACZ7G,IAAA,CAAChC,MAAM,EAACuL,OAAO,CAAEA,CAAA,GAAMtI,mBAAmB,CAAC,KAAK,CAAE,CAAA4F,QAAA,CAAC,QAAM,CAAQ,CAAC,cAClE7G,IAAA,CAAChC,MAAM,EAACuL,OAAO,CAAExE,gBAAiB,CAACmB,KAAK,CAAC,OAAO,CAACe,OAAO,CAAC,WAAW,CAAAJ,QAAA,CAAC,QAErE,CAAQ,CAAC,EACI,CAAC,EACV,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAA1G,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}