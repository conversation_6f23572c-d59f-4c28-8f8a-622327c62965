import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  ShoppingCart,
  History,
} from '@mui/icons-material';
import CreditBalance from '../../components/credit/CreditBalance';
import CreditPackages from '../../components/credit/CreditPackages';
import TransactionHistory from '../../components/credit/TransactionHistory';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`wallet-tabpanel-${index}`}
      aria-labelledby={`wallet-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `wallet-tab-${index}`,
    'aria-controls': `wallet-tabpanel-${index}`,
  };
}

const Wallet: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Check for payment status in URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const billplzId = urlParams.get('billplz[id]');
    const billplzPaid = urlParams.get('billplz[paid]');
    const billplzState = urlParams.get('billplz[state]');

    if (billplzId) {
      if (billplzState === 'paid' || billplzPaid === 'true') {
        setNotification({
          open: true,
          message: 'Payment successful! Your wallet has been topped up.',
          severity: 'success',
        });
        setRefreshTrigger(prev => prev + 1);
      } else {
        setNotification({
          open: true,
          message: 'Payment was not completed. Please try again or contact support.',
          severity: 'warning',
        });
      }

      // Clean up URL parameters
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handlePurchaseSuccess = () => {
    setRefreshTrigger(prev => prev + 1);
    setNotification({
      open: true,
      message: 'Top up initiated! You will be redirected to complete payment.',
      severity: 'info',
    });
  };

  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Wallet Management
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Manage your wallet balance, top up your wallet, and view your transaction history.
      </Typography>

      {/* Wallet Balance Overview */}
      <Box mb={4}>
        <CreditBalance refreshTrigger={refreshTrigger} />
      </Box>

      {/* Tabs */}
      <Paper sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="wallet management tabs"
            variant="fullWidth"
          >
            <Tab
              icon={<ShoppingCart />}
              label="Wallet Top-Up"
              {...a11yProps(0)}
            />
            <Tab
              icon={<History />}
              label="Transaction History"
              {...a11yProps(1)}
            />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <CreditPackages onPurchaseSuccess={handlePurchaseSuccess} />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <TransactionHistory refreshTrigger={refreshTrigger} />
        </TabPanel>
      </Paper>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Wallet;
