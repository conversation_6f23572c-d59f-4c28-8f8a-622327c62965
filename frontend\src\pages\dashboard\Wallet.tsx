import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Alert,
  Snackbar,
  Container,
  useTheme,
  useMediaQuery,
  Fade,
} from '@mui/material';
import {
  Add,
  History,
  AccountBalanceWallet,
} from '@mui/icons-material';
import WalletBalance from '../../components/wallet/WalletBalance';
import WalletTopUp from '../../components/wallet/WalletTopUp';
import WalletTransactionHistory from '../../components/wallet/WalletTransactionHistory';
import creditService, { CreditStatistics } from '../../services/creditService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`wallet-tabpanel-${index}`}
      aria-labelledby={`wallet-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `wallet-tab-${index}`,
    'aria-controls': `wallet-tabpanel-${index}`,
  };
}

const Wallet: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [statistics, setStatistics] = useState<CreditStatistics | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));

  // Fetch wallet statistics
  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        const data = await creditService.getStatistics();
        setStatistics(data);
      } catch (error) {
        console.error('Failed to fetch wallet statistics:', error);
      }
    };
    fetchStatistics();
  }, [refreshTrigger]);

  // Check for payment status in URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const billplzId = urlParams.get('billplz[id]');
    const billplzPaid = urlParams.get('billplz[paid]');
    const billplzState = urlParams.get('billplz[state]');

    if (billplzId) {
      if (billplzState === 'paid' || billplzPaid === 'true') {
        setNotification({
          open: true,
          message: 'Payment successful! Your wallet has been topped up.',
          severity: 'success',
        });
        setRefreshTrigger(prev => prev + 1);
      } else {
        setNotification({
          open: true,
          message: 'Payment was not completed. Please try again or contact support.',
          severity: 'warning',
        });
      }

      // Clean up URL parameters
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleTopUpSuccess = () => {
    setRefreshTrigger(prev => prev + 1);
    setNotification({
      open: true,
      message: 'Top up initiated! You will be redirected to complete payment.',
      severity: 'info',
    });
  };

  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  const handleTopUpClick = () => {
    setTabValue(0); // Switch to top-up tab
  };

  const handleHistoryClick = () => {
    setTabValue(1); // Switch to history tab
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    setRefreshTrigger(prev => prev + 1);
    // Add a small delay to show the refresh animation
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  const currentBalance = statistics?.current_balance || 0;

  return (
    <Container maxWidth="lg" sx={{ py: 2 }}>
      <Fade in timeout={800}>
        <Box>
          {/* Header */}
          <Box mb={4} textAlign={isMobile ? 'center' : 'left'}>
            <Typography
              variant="h3"
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 300,
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                justifyContent: isMobile ? 'center' : 'flex-start',
              }}
            >
              <AccountBalanceWallet color="primary" sx={{ fontSize: 40 }} />
              Wallet Management
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 300 }}>
              Manage your Malaysian Ringgit (RM) wallet balance, top up funds, and track transactions
            </Typography>
          </Box>

          {/* Wallet Balance Overview */}
          <WalletBalance
            refreshTrigger={refreshTrigger}
            onTopUpClick={handleTopUpClick}
            onHistoryClick={handleHistoryClick}
          />



          {/* Main Content Tabs */}
          <Paper
            sx={{
              width: '100%',
              borderRadius: 3,
              overflow: 'hidden',
              boxShadow: theme.shadows[4],
            }}
          >
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="wallet management tabs"
                variant={isMobile ? 'fullWidth' : 'standard'}
                sx={{
                  '& .MuiTab-root': {
                    minHeight: 64,
                    fontSize: '1rem',
                    fontWeight: 500,
                  },
                }}
              >
                <Tab
                  icon={<Add />}
                  label="Top Up Wallet"
                  iconPosition="start"
                  {...a11yProps(0)}
                />
                <Tab
                  icon={<History />}
                  label="Transaction History"
                  iconPosition="start"
                  {...a11yProps(1)}
                />
              </Tabs>
            </Box>

            <TabPanel value={tabValue} index={0}>
              <WalletTopUp
                onTopUpSuccess={handleTopUpSuccess}
                currentBalance={currentBalance}
              />
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <WalletTransactionHistory refreshTrigger={refreshTrigger} />
            </TabPanel>
          </Paper>
        </Box>
      </Fade>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{
            width: '100%',
            borderRadius: 2,
            boxShadow: theme.shadows[8],
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Wallet;
