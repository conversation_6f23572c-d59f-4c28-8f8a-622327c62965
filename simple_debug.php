<?php

require_once 'backend/bootstrap/app.php';

$app = \Illuminate\Foundation\Application::configure(basePath: 'backend')
    ->withRouting(
        web: __DIR__.'/backend/routes/web.php',
        api: __DIR__.'/backend/routes/api.php',
        commands: __DIR__.'/backend/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (\Illuminate\Foundation\Configuration\Middleware $middleware) {
        //
    })
    ->withExceptions(function (\Illuminate\Foundation\Configuration\Exceptions $exceptions) {
        //
    })
    ->create();

$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== USER BALANCE DEBUG ===\n\n";

try {
    // Check all users
    echo "1. All users in database:\n";
    $users = \App\Models\User::select('id', 'email', 'name', 'wallet_balance')->get();
    foreach ($users as $user) {
        echo "   - {$user->name} ({$user->email}) - Balance: RM " . number_format($user->wallet_balance, 2) . "\n";
    }

    // Look for <PERSON> Ho specifically
    echo "\n2. Looking for Jason Ho:\n";
    $jason = \App\Models\User::where('name', 'Jason Ho')->first();
    
    if ($jason) {
        echo "   ✅ Found Jason Ho\n";
        echo "   ID: {$jason->id}\n";
        echo "   Name: {$jason->name}\n";
        echo "   Email: {$jason->email}\n";
        echo "   Wallet Balance: RM " . number_format($jason->wallet_balance, 2) . "\n";

        // Check transactions
        echo "\n3. Wallet transactions for Jason Ho:\n";
        $transactions = $jason->walletTransactions()->orderBy('created_at', 'desc')->get();
        echo "   Total transactions: " . $transactions->count() . "\n";

        foreach ($transactions as $transaction) {
            $amount = $transaction->amount >= 0 ? '+' : '';
            echo "   - {$transaction->type}: {$amount}RM " . number_format(abs($transaction->amount), 2) . " ({$transaction->created_at})\n";
        }

    } else {
        echo "   ❌ Jason Ho not found\n";
        echo "   Creating Jason Ho user...\n";

        $jason = \App\Models\User::create([
            'name' => 'Jason Ho',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'role' => 'user',
            'is_active' => true,
            'wallet_balance' => 150.00,
            'email_verified_at' => now(),
        ]);

        echo "   ✅ Created Jason Ho (ID: {$jason->id}) with RM 150.00 balance\n";

        // Add some sample transactions
        $jason->addToWallet(100.00, 'Initial wallet top-up', 'top_up');
        $jason->addToWallet(50.00, 'Welcome bonus', 'bonus');

        echo "   ✅ Added sample transactions\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== DEBUG COMPLETE ===\n";
