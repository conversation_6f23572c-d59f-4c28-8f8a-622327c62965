import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  LinearProgress,
  Chip,
  useTheme,
} from '@mui/material';
import {
  Warning,
  Error,
  Info,
  AccountBalanceWallet,
  Add,
} from '@mui/icons-material';
import creditService from '../../services/creditService';

interface OverdraftPreventionProps {
  currentBalance: number;
  requiredAmount?: number;
  onTopUpClick?: () => void;
  showAsCard?: boolean;
}

const OverdraftPrevention: React.FC<OverdraftPreventionProps> = ({
  currentBalance,
  requiredAmount = 0,
  onTopUpClick,
  showAsCard = false,
}) => {
  const theme = useTheme();

  const isInsufficientFunds = requiredAmount > 0 && currentBalance < requiredAmount;
  const shortfall = Math.max(0, requiredAmount - currentBalance);
  const balancePercentage = requiredAmount > 0 ? Math.min(100, (currentBalance / requiredAmount) * 100) : 100;

  // Determine warning level based on balance
  const getWarningLevel = () => {
    if (currentBalance <= 0) return 'empty';
    if (currentBalance < 10) return 'critical';
    if (currentBalance < 50) return 'low';
    return 'sufficient';
  };

  const warningLevel = getWarningLevel();

  const getAlertSeverity = (): "error" | "warning" | "info" | "success" => {
    if (isInsufficientFunds) return 'error';
    switch (warningLevel) {
      case 'empty':
        return 'error';
      case 'critical':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'success';
    }
  };

  const getAlertIcon = () => {
    if (isInsufficientFunds) return <Error />;
    switch (warningLevel) {
      case 'empty':
        return <Error />;
      case 'critical':
        return <Warning />;
      case 'low':
        return <Info />;
      default:
        return <AccountBalanceWallet />;
    }
  };

  const getAlertTitle = () => {
    if (isInsufficientFunds) return 'Insufficient Funds';
    switch (warningLevel) {
      case 'empty':
        return 'Wallet Empty';
      case 'critical':
        return 'Critical Balance';
      case 'low':
        return 'Low Balance Warning';
      default:
        return 'Sufficient Balance';
    }
  };

  const getAlertMessage = () => {
    if (isInsufficientFunds) {
      return `You need ${creditService.formatWalletBalance(shortfall)} more to complete this transaction.`;
    }
    
    switch (warningLevel) {
      case 'empty':
        return 'Your wallet is empty. Add funds to start making transactions.';
      case 'critical':
        return 'Your wallet balance is critically low. Consider topping up soon.';
      case 'low':
        return 'Your wallet balance is getting low. You may want to add more funds.';
      default:
        return 'Your wallet has sufficient funds for transactions.';
    }
  };

  const getProgressColor = (): "primary" | "secondary" | "error" | "info" | "success" | "warning" => {
    if (isInsufficientFunds) return 'error';
    switch (warningLevel) {
      case 'empty':
        return 'error';
      case 'critical':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'success';
    }
  };

  // Don't show anything if balance is sufficient and no specific amount is required
  if (!isInsufficientFunds && warningLevel === 'sufficient' && requiredAmount === 0) {
    return null;
  }

  const content = (
    <Box>
      <Alert 
        severity={getAlertSeverity()} 
        icon={getAlertIcon()}
        sx={{ mb: requiredAmount > 0 ? 2 : 0 }}
      >
        <Typography variant="body2" fontWeight={600} gutterBottom>
          {getAlertTitle()}
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          {getAlertMessage()}
        </Typography>
        
        <Box display="flex" alignItems="center" gap={2} flexWrap="wrap">
          <Chip
            label={`Current: ${creditService.formatWalletBalance(currentBalance)}`}
            size="small"
            color={getAlertSeverity()}
            variant="outlined"
          />
          
          {requiredAmount > 0 && (
            <Chip
              label={`Required: ${creditService.formatWalletBalance(requiredAmount)}`}
              size="small"
              color="default"
              variant="outlined"
            />
          )}
          
          {isInsufficientFunds && (
            <Chip
              label={`Shortfall: ${creditService.formatWalletBalance(shortfall)}`}
              size="small"
              color="error"
              variant="filled"
            />
          )}
        </Box>

        {onTopUpClick && (warningLevel !== 'sufficient' || isInsufficientFunds) && (
          <Box mt={2}>
            <Button
              variant="contained"
              size="small"
              startIcon={<Add />}
              onClick={onTopUpClick}
              color={isInsufficientFunds ? 'error' : 'primary'}
            >
              Top Up Wallet
            </Button>
          </Box>
        )}
      </Alert>

      {/* Progress bar for required amount */}
      {requiredAmount > 0 && (
        <Box>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
            <Typography variant="caption" color="text.secondary">
              Balance Coverage
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {balancePercentage.toFixed(0)}%
            </Typography>
          </Box>
          
          <LinearProgress
            variant="determinate"
            value={balancePercentage}
            color={getProgressColor()}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: theme.palette.grey[200],
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
              },
            }}
          />
          
          <Box display="flex" justifyContent="space-between" mt={1}>
            <Typography variant="caption" color="text.secondary">
              {creditService.formatWalletBalance(0)}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {creditService.formatWalletBalance(requiredAmount)}
            </Typography>
          </Box>
        </Box>
      )}
    </Box>
  );

  if (showAsCard) {
    return (
      <Card variant="outlined" sx={{ mb: 2 }}>
        <CardContent>
          {content}
        </CardContent>
      </Card>
    );
  }

  return content;
};

export default OverdraftPrevention;
