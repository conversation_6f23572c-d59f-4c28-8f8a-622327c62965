import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  TextField,
  InputAdornment,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Divider,
  CircularProgress,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Add,
  Payment,
  Security,
  CheckCircle,
  Info,
} from '@mui/icons-material';
import creditService, { PaymentConfig } from '../../services/creditService';

interface WalletTopUpProps {
  onTopUpSuccess?: () => void;
  currentBalance?: number;
}

interface TopUpAmount {
  value: number;
  label: string;
  popular?: boolean;
  bonus?: number;
}

const WalletTopUp: React.FC<WalletTopUpProps> = ({
  onTopUpSuccess,
  currentBalance = 0,
}) => {
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null);
  const [customAmount, setCustomAmount] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    amount: number;
  }>({ open: false, amount: 0 });
  const [paymentConfig, setPaymentConfig] = useState<PaymentConfig | null>(null);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Predefined top-up amounts in RM
  const topUpAmounts: TopUpAmount[] = [
    { value: 10, label: 'RM 10.00' },
    { value: 20, label: 'RM 20.00', popular: true },
    { value: 50, label: 'RM 50.00', popular: true },
    { value: 100, label: 'RM 100.00', bonus: 5 },
    { value: 200, label: 'RM 200.00', bonus: 15 },
    { value: 500, label: 'RM 500.00', bonus: 50 },
  ];

  useEffect(() => {
    fetchPaymentConfig();
  }, []);

  const fetchPaymentConfig = async () => {
    try {
      // For now, assume Billplz is configured
      setPaymentConfig({
        billplz_enabled: true,
        billplz_configured: true,
      });
    } catch (err) {
      console.error('Failed to fetch payment config:', err);
    }
  };

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount);
    setCustomAmount('');
    setError(null);
  };

  const handleCustomAmountChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setCustomAmount(value);
    setSelectedAmount(null);
    setError(null);
  };

  const getSelectedAmount = (): number => {
    if (selectedAmount !== null) return selectedAmount;
    if (customAmount) {
      const parsed = parseFloat(customAmount);
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  };

  const validateAmount = (amount: number): string | null => {
    if (amount <= 0) return 'Please select or enter a valid amount';
    if (amount < 1) return 'Minimum top-up amount is RM 1.00';
    if (amount > 10000) return 'Maximum top-up amount is RM 10,000.00';
    return null;
  };

  const handleTopUpClick = () => {
    const amount = getSelectedAmount();
    const validationError = validateAmount(amount);
    
    if (validationError) {
      setError(validationError);
      return;
    }

    setConfirmDialog({ open: true, amount });
  };

  const handleConfirmTopUp = async () => {
    const amount = confirmDialog.amount;
    
    try {
      setLoading(true);
      setError(null);
      
      // Create a temporary package object for the payment
      const tempPackage = {
        id: 999, // Temporary ID
        name: `RM ${amount.toFixed(2)} Top-up`,
        price: amount,
        credits: amount, // 1:1 conversion
      };

      const response = await creditService.createPayment(
        tempPackage.id,
        `${window.location.origin}/dashboard/wallet`
      );

      if (response.success && response.payment_url) {
        if (onTopUpSuccess) {
          onTopUpSuccess();
        }
        // Redirect to Billplz payment page
        window.location.href = response.payment_url;
      } else {
        setError(response.error || 'Payment creation failed');
      }
    } catch (err: any) {
      console.error('Payment creation error:', err);
      const errorMessage = err.response?.data?.error ||
                          err.response?.data?.message ||
                          err.message ||
                          'Failed to create payment. Please try again.';
      setError(errorMessage);
    } finally {
      setLoading(false);
      setConfirmDialog({ open: false, amount: 0 });
    }
  };

  const getBonusAmount = (amount: number): number => {
    const amountConfig = topUpAmounts.find(a => a.value === amount);
    return amountConfig?.bonus || 0;
  };

  const getTotalAmount = (amount: number): number => {
    return amount + getBonusAmount(amount);
  };

  if (!paymentConfig?.billplz_configured) {
    return (
      <Alert severity="warning" sx={{ mb: 3 }}>
        <Typography variant="body1" gutterBottom>
          Wallet top-up is currently unavailable.
        </Typography>
        <Typography variant="body2">
          Payment gateway is not configured. Please contact support for assistance.
        </Typography>
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Add color="primary" />
        Top Up Wallet
      </Typography>
      
      <Typography variant="body2" color="text.secondary" paragraph>
        Add money to your wallet using secure payment methods. All amounts are in Malaysian Ringgit (RM).
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Current Balance Info */}
      <Card variant="outlined" sx={{ mb: 3, backgroundColor: 'grey.50' }}>
        <CardContent sx={{ py: 2 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="body2" color="text.secondary">
              Current Balance:
            </Typography>
            <Typography variant="h6" color="primary">
              {creditService.formatWalletBalance(currentBalance)}
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Predefined Amounts */}
      <Typography variant="h6" gutterBottom>
        Quick Top-Up
      </Typography>
      
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: 'repeat(2, 1fr)',
            sm: 'repeat(3, 1fr)',
            md: 'repeat(6, 1fr)'
          },
          gap: 2,
          mb: 3
        }}
      >
        {topUpAmounts.map((amount) => (
          <Card
            key={amount.value}
            sx={{
              cursor: 'pointer',
              border: selectedAmount === amount.value ? 2 : 1,
              borderColor: selectedAmount === amount.value ? 'primary.main' : 'divider',
              backgroundColor: selectedAmount === amount.value ? 'primary.50' : 'background.paper',
              transition: 'all 0.2s',
              position: 'relative',
              '&:hover': {
                borderColor: 'primary.main',
                transform: 'translateY(-2px)',
                boxShadow: 2,
              },
            }}
            onClick={() => handleAmountSelect(amount.value)}
          >
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              {amount.popular && (
                <Chip
                  label="Popular"
                  size="small"
                  color="primary"
                  sx={{ position: 'absolute', top: -8, right: 8, fontSize: '0.7rem' }}
                />
              )}
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {amount.label}
              </Typography>
              {amount.bonus && (
                <Typography variant="caption" color="success.main" sx={{ fontWeight: 600 }}>
                  +RM {amount.bonus.toFixed(2)} bonus
                </Typography>
              )}
            </CardContent>
          </Card>
        ))}
      </Box>

      {/* Custom Amount */}
      <Typography variant="h6" gutterBottom>
        Custom Amount
      </Typography>
      
      <TextField
        fullWidth
        label="Enter custom amount"
        value={customAmount}
        onChange={handleCustomAmountChange}
        type="number"
        inputProps={{ min: 1, max: 10000, step: 0.01 }}
        InputProps={{
          startAdornment: <InputAdornment position="start">RM</InputAdornment>,
        }}
        helperText="Minimum: RM 1.00 | Maximum: RM 10,000.00"
        sx={{ mb: 3 }}
      />

      {/* Payment Security Info */}
      <Card variant="outlined" sx={{ mb: 3, backgroundColor: 'info.50' }}>
        <CardContent sx={{ py: 2 }}>
          <Box display="flex" alignItems="center" gap={1} mb={1}>
            <Security color="info" fontSize="small" />
            <Typography variant="body2" fontWeight={600}>
              Secure Payment with Billplz
            </Typography>
          </Box>
          <Typography variant="caption" color="text.secondary">
            Your payment is processed securely through Billplz. We never store your payment information.
          </Typography>
        </CardContent>
      </Card>

      {/* Top Up Button */}
      <Button
        variant="contained"
        size="large"
        fullWidth
        onClick={handleTopUpClick}
        disabled={getSelectedAmount() <= 0 || loading}
        startIcon={loading ? <CircularProgress size={20} /> : <Payment />}
        sx={{ py: 1.5 }}
      >
        {loading ? 'Processing...' : `Top Up ${creditService.formatWalletBalance(getSelectedAmount())}`}
      </Button>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialog.open} onClose={() => setConfirmDialog({ open: false, amount: 0 })}>
        <DialogTitle>Confirm Top-Up</DialogTitle>
        <DialogContent>
          <Box sx={{ py: 2 }}>
            <Typography variant="body1" gutterBottom>
              You are about to top up your wallet with:
            </Typography>
            
            <Box sx={{ my: 2, p: 2, backgroundColor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="h6" color="primary">
                {creditService.formatWalletBalance(confirmDialog.amount)}
              </Typography>
              
              {getBonusAmount(confirmDialog.amount) > 0 && (
                <>
                  <Typography variant="body2" color="success.main">
                    + {creditService.formatWalletBalance(getBonusAmount(confirmDialog.amount))} bonus
                  </Typography>
                  <Divider sx={{ my: 1 }} />
                  <Typography variant="body2" fontWeight={600}>
                    Total: {creditService.formatWalletBalance(getTotalAmount(confirmDialog.amount))}
                  </Typography>
                </>
              )}
            </Box>

            <Alert severity="info" icon={<Info />}>
              You will be redirected to Billplz to complete your payment securely.
            </Alert>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialog({ open: false, amount: 0 })}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleConfirmTopUp}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={16} /> : <CheckCircle />}
          >
            {loading ? 'Processing...' : 'Proceed to Payment'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WalletTopUp;
