<?php

namespace App\Models;

use App\Services\WalletTransactionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WalletTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'package_id',
        'type',
        'amount',
        'amount_paid',
        'payment_method',
        'payment_reference',
        'payment_status',
        'description',
        'processed_at',
        'withdrawal_method',
        'withdrawal_reference',
        'withdrawal_processed_at',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'processed_at' => 'datetime',
        'withdrawal_processed_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        // When a transaction is being created, apply business rules
        static::creating(function (WalletTransaction $transaction) {
            $transaction->applyBusinessRules();
        });

        // When a transaction is created
        static::created(function (WalletTransaction $transaction) {
            $transaction->updateUserBalance();
        });

        // When a transaction is updated (e.g., status changed from pending to completed)
        static::updated(function (WalletTransaction $transaction) {
            // Only update balance if payment_status changed to completed
            if ($transaction->wasChanged('payment_status') && $transaction->payment_status === 'completed') {
                $transaction->updateUserBalance();
            }
        });

        // When a transaction is deleted
        static::deleted(function (WalletTransaction $transaction) {
            if ($transaction->payment_status === 'completed') {
                // Reverse the transaction amount
                $transaction->user->decrement('wallet_balance', $transaction->amount);
            }
        });
    }

    /**
     * Update user's wallet balance based on this transaction
     */
    protected function updateUserBalance(): void
    {
        // Only update balance for completed transactions
        if ($this->payment_status !== 'completed') {
            return;
        }

        $initialBalance = $this->user->wallet_balance;
        
        // Update user's wallet balance with transaction amount
        $this->user->increment('wallet_balance', $this->amount);
        
        $finalBalance = $this->user->fresh()->wallet_balance;
        $balanceChange = $finalBalance - $initialBalance;

        // Log the balance update
        \Log::info('Wallet balance updated via transaction', [
            'transaction_id' => $this->id,
            'user_id' => $this->user_id,
            'amount' => $this->amount,
            'initial_balance' => $initialBalance,
            'final_balance' => $finalBalance,
            'balance_change' => $balanceChange,
            'transaction_type' => $this->type,
            'payment_method' => $this->payment_method,
        ]);
    }

    /**
     * Get the user that owns the transaction
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the package associated with the transaction (if any)
     * Note: CreditPackage model has been removed, but package_id is kept for data integrity
     */
    public function package(): BelongsTo
    {
        // Return a dummy relationship since CreditPackage model no longer exists
        // This prevents errors for existing transactions that have package_id set
        return $this->belongsTo(User::class, 'package_id')->whereRaw('0 = 1');
    }

    /**
     * Legacy relationship for backward compatibility
     * @deprecated CreditPackage functionality has been removed
     */
    public function creditPackage(): BelongsTo
    {
        return $this->package();
    }

    /**
     * Apply business rules based on transaction type
     */
    protected function applyBusinessRules(): void
    {
        if (!$this->type) {
            return;
        }

        $service = new WalletTransactionService();
        
        try {
            // Process the transaction data using the service
            $data = $this->toArray();
            $processedData = $service->processTransactionData($data);
            
            // Apply the processed data to the model
            foreach ($processedData as $key => $value) {
                if ($this->isFillable($key)) {
                    $this->$key = $value;
                }
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to apply business rules to wallet transaction', [
                'transaction_type' => $this->type,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get transaction type configuration
     */
    public function getTypeConfig(): array
    {
        $service = new WalletTransactionService();
        return $service->getTransactionTypeConfig($this->type);
    }

    /**
     * Check if transaction type requires payment amount
     */
    public function requiresPaymentAmount(): bool
    {
        $config = $this->getTypeConfig();
        return $config['requires_payment_amount'] ?? false;
    }

    /**
     * Check if transaction type requires payment method
     */
    public function requiresPaymentMethod(): bool
    {
        $config = $this->getTypeConfig();
        return $config['requires_payment_method'] ?? false;
    }

    /**
     * Check if transaction type allows package selection
     * @deprecated Package selection has been removed
     */
    public function allowsPackageSelection(): bool
    {
        // Package selection is no longer supported
        return false;
    }

    /**
     * Get expected amount sign for this transaction type
     */
    public function getExpectedAmountSign(): string
    {
        $config = $this->getTypeConfig();
        return $config['amount_sign'] ?? 'flexible';
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmount(): string
    {
        return 'RM ' . number_format($this->amount, 2);
    }

    /**
     * Get formatted amount paid
     */
    public function getFormattedAmountPaid(): string
    {
        if ($this->amount_paid === null) {
            return 'N/A';
        }
        return 'RM ' . number_format($this->amount_paid, 2);
    }

    /**
     * Check if transaction is a credit (positive amount)
     */
    public function isCredit(): bool
    {
        return $this->amount > 0;
    }

    /**
     * Check if transaction is a debit (negative amount)
     */
    public function isDebit(): bool
    {
        return $this->amount < 0;
    }

    /**
     * Get transaction status badge color
     */
    public function getStatusBadgeColor(): string
    {
        return match ($this->payment_status) {
            'completed' => 'success',
            'pending' => 'warning',
            'failed' => 'danger',
            'refunded' => 'info',
            default => 'gray',
        };
    }

    /**
     * Get transaction type badge color
     */
    public function getTypeBadgeColor(): string
    {
        return match ($this->type) {
            'top_up' => 'success',
            'payment' => 'warning',
            'withdrawal' => 'danger',
            'refund' => 'info',
            'bonus' => 'primary',
            'adjustment' => 'gray',
            default => 'gray',
        };
    }

    /**
     * Validate transaction against business rules
     */
    public function validateBusinessRules(): array
    {
        $errors = [];
        
        // Check amount sign
        $expectedSign = $this->getExpectedAmountSign();
        
        switch ($expectedSign) {
            case 'positive':
                if ($this->amount <= 0) {
                    $errors[] = "Amount must be positive for {$this->type} transactions.";
                }
                break;
            case 'negative':
                if ($this->amount >= 0) {
                    $errors[] = "Amount must be negative for {$this->type} transactions.";
                }
                break;
        }
        
        // Check required fields
        if ($this->requiresPaymentAmount() && empty($this->amount_paid)) {
            $errors[] = "Payment amount is required for {$this->type} transactions.";
        }
        
        if ($this->requiresPaymentMethod() && empty($this->payment_method)) {
            $errors[] = "Payment method is required for {$this->type} transactions.";
        }
        
        // Check user has sufficient balance for negative transactions
        if ($this->amount < 0 && $this->user) {
            $requiredAmount = abs($this->amount);
            if ($this->user->wallet_balance < $requiredAmount) {
                $errors[] = "Insufficient wallet balance. User has RM " . number_format($this->user->wallet_balance, 2) . ", but RM " . number_format($requiredAmount, 2) . " required.";
            }
        }
        
        return $errors;
    }
}
