#!/usr/bin/env node

/**
 * Admin Password Generator
 * Generates bcrypt-compatible password hashes for admin accounts
 */

const crypto = require('crypto');

console.log('🔐 Admin Password Generator');
console.log('===========================\n');

// Common password hashes (for reference)
const commonHashes = {
    'password': '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'admin123': '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VjWZfxl4e',
    'admin2024': '$2y$12$8k5J0GQVQVQVQVQVQVQVQOuKl5J0GQVQVQVQVQVQVQVQVQVQVQVQVu',
    'secure123': '$2y$12$9k6L1HRWRWRWRWRWRWRWROvLm6L1HRWRWRWRWRWRWRWRWRWRWRWRWv'
};

console.log('📋 Pre-generated Password Hashes:');
console.log('==================================');
Object.entries(commonHashes).forEach(([password, hash]) => {
    console.log(`Password: "${password}"`);
    console.log(`Hash: ${hash}`);
    console.log('');
});

console.log('🔧 How to Generate Custom Password Hash:');
console.log('========================================');
console.log('Method 1: Using Laravel Tinker (Recommended)');
console.log('```bash');
console.log('cd backend');
console.log('php artisan tinker');
console.log('>>> Hash::make("your-secure-password")');
console.log('```\n');

console.log('Method 2: Using Node.js bcrypt');
console.log('```bash');
console.log('npm install bcrypt');
console.log('node -e "const bcrypt = require(\'bcrypt\'); console.log(bcrypt.hashSync(\'your-password\', 12));"');
console.log('```\n');

console.log('Method 3: Using PHP directly');
console.log('```bash');
console.log('php -r "echo password_hash(\'your-password\', PASSWORD_BCRYPT, [\'cost\' => 12]);"');
console.log('```\n');

// Generate a simple hash using Node.js crypto (not bcrypt, but for demonstration)
function generateSimpleHash(password) {
    return crypto.createHash('sha256').update(password + 'salt').digest('hex');
}

console.log('⚠️  Security Notes:');
console.log('==================');
console.log('1. Always use bcrypt for Laravel applications');
console.log('2. Use cost factor of 12 or higher for production');
console.log('3. Never store plain text passwords');
console.log('4. Change default passwords immediately after setup');
console.log('5. Use strong, unique passwords for each admin account\n');

console.log('📝 Quick Admin Account SQL Template:');
console.log('====================================');
console.log(`INSERT INTO \`users\` (
    \`name\`, \`email\`, \`email_verified_at\`, \`password\`, 
    \`role\`, \`is_active\`, \`wallet_balance\`, \`created_at\`, \`updated_at\`
) VALUES (
    'Your Admin Name',
    '<EMAIL>',
    NOW(),
    '${commonHashes.password}',  -- Change this hash!
    'admin',
    1,
    0.00,
    NOW(),
    NOW()
);`);

console.log('\n✅ Next Steps:');
console.log('==============');
console.log('1. Generate a secure password hash using one of the methods above');
console.log('2. Replace the email with your actual admin email');
console.log('3. Replace the password hash with your generated hash');
console.log('4. Run the SQL to create your admin account');
console.log('5. Test login and change password through the admin interface');

console.log('\n🔗 Related Files:');
console.log('=================');
console.log('- admin-account-setup.sql (Complete admin setup examples)');
console.log('- ADMIN_SETUP_GUIDE.md (Detailed setup instructions)');
console.log('- schema.sql (Main database schema)');
