#!/usr/bin/env node

/**
 * SQL Schema Validation Script
 * This script validates the SQL syntax and table dependencies in schema.sql
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 SQL Schema Validation');
console.log('========================\n');

function validateTableOrder() {
    console.log('1. Validating Table Creation Order...');
    
    try {
        const schemaPath = path.join(__dirname, 'schema.sql');
        const content = fs.readFileSync(schemaPath, 'utf8');
        
        // Extract CREATE TABLE statements and their order
        const createTableRegex = /CREATE TABLE `([^`]+)`/g;
        const tables = [];
        let match;
        
        while ((match = createTableRegex.exec(content)) !== null) {
            tables.push(match[1]);
        }
        
        console.log('   📋 Tables found in order:');
        tables.forEach((table, index) => {
            console.log(`   ${index + 1}. ${table}`);
        });
        
        // Check critical dependencies
        const usersIndex = tables.indexOf('users');
        const creditPackagesIndex = tables.indexOf('credit_packages');
        const walletTransactionsIndex = tables.indexOf('wallet_transactions');
        const printingCategoriesIndex = tables.indexOf('printing_categories');
        const printingProductsIndex = tables.indexOf('printing_products');
        const printingOrdersIndex = tables.indexOf('printing_orders');
        
        console.log('\n   🔗 Checking dependencies:');
        
        // Users should be created first (referenced by many tables)
        if (usersIndex === 0) {
            console.log('   ✅ Users table created first: PASS');
        } else {
            console.log('   ❌ Users table not created first: FAIL');
            return false;
        }
        
        // Credit packages should be created before wallet transactions
        if (creditPackagesIndex < walletTransactionsIndex) {
            console.log('   ✅ Credit packages before wallet transactions: PASS');
        } else {
            console.log('   ❌ Credit packages after wallet transactions: FAIL');
            return false;
        }
        
        // Printing categories should be created before printing products
        if (printingCategoriesIndex < printingProductsIndex) {
            console.log('   ✅ Printing categories before products: PASS');
        } else {
            console.log('   ❌ Printing categories after products: FAIL');
            return false;
        }
        
        // Printing products should be created before printing orders
        if (printingProductsIndex < printingOrdersIndex) {
            console.log('   ✅ Printing products before orders: PASS');
        } else {
            console.log('   ❌ Printing products after orders: FAIL');
            return false;
        }
        
        console.log('   🎉 Table creation order: PASS\n');
        return true;
        
    } catch (error) {
        console.log(`   ❌ Error validating table order: ${error.message}\n`);
        return false;
    }
}

function validateForeignKeyReferences() {
    console.log('2. Validating Foreign Key References...');
    
    try {
        const schemaPath = path.join(__dirname, 'schema.sql');
        const content = fs.readFileSync(schemaPath, 'utf8');
        
        // Extract foreign key constraints
        const foreignKeyRegex = /CONSTRAINT `([^`]+)` FOREIGN KEY \(`([^`]+)`\) REFERENCES `([^`]+)` \(`([^`]+)`\)/g;
        const foreignKeys = [];
        let match;
        
        while ((match = foreignKeyRegex.exec(content)) !== null) {
            foreignKeys.push({
                constraint: match[1],
                column: match[2],
                referencedTable: match[3],
                referencedColumn: match[4]
            });
        }
        
        console.log(`   📋 Found ${foreignKeys.length} foreign key constraints:`);
        
        let allValid = true;
        foreignKeys.forEach((fk, index) => {
            console.log(`   ${index + 1}. ${fk.constraint}: ${fk.column} → ${fk.referencedTable}.${fk.referencedColumn}`);
            
            // Check if referenced table exists in schema
            const tableExists = content.includes(`CREATE TABLE \`${fk.referencedTable}\``);
            if (!tableExists) {
                console.log(`      ❌ Referenced table '${fk.referencedTable}' not found`);
                allValid = false;
            }
        });
        
        if (allValid) {
            console.log('   🎉 Foreign key references: PASS\n');
            return true;
        } else {
            console.log('   ❌ Foreign key references: FAIL\n');
            return false;
        }
        
    } catch (error) {
        console.log(`   ❌ Error validating foreign keys: ${error.message}\n`);
        return false;
    }
}

function validateSQLSyntax() {
    console.log('3. Validating SQL Syntax...');
    
    try {
        const schemaPath = path.join(__dirname, 'schema.sql');
        const content = fs.readFileSync(schemaPath, 'utf8');
        
        // Basic syntax checks
        const checks = [
            {
                name: 'Balanced parentheses',
                test: () => {
                    const openCount = (content.match(/\(/g) || []).length;
                    const closeCount = (content.match(/\)/g) || []).length;
                    return openCount === closeCount;
                }
            },
            {
                name: 'Balanced backticks',
                test: () => {
                    const backtickCount = (content.match(/`/g) || []).length;
                    return backtickCount % 2 === 0;
                }
            },
            {
                name: 'No syntax errors in CREATE TABLE',
                test: () => {
                    return !content.includes('CREATE TABLE CREATE TABLE') && 
                           !content.includes('PRIMARY KEY PRIMARY KEY');
                }
            },
            {
                name: 'Proper ENGINE specification',
                test: () => {
                    const engineCount = (content.match(/ENGINE=InnoDB/g) || []).length;
                    const tableCount = (content.match(/CREATE TABLE/g) || []).length;
                    return engineCount === tableCount;
                }
            },
            {
                name: 'Proper charset specification',
                test: () => {
                    const charsetCount = (content.match(/CHARSET=utf8mb4/g) || []).length;
                    const tableCount = (content.match(/CREATE TABLE/g) || []).length;
                    return charsetCount === tableCount;
                }
            }
        ];
        
        let allPassed = true;
        checks.forEach((check, index) => {
            const result = check.test();
            console.log(`   ${index + 1}. ${check.name}: ${result ? '✅ PASS' : '❌ FAIL'}`);
            if (!result) allPassed = false;
        });
        
        if (allPassed) {
            console.log('   🎉 SQL syntax validation: PASS\n');
            return true;
        } else {
            console.log('   ❌ SQL syntax validation: FAIL\n');
            return false;
        }
        
    } catch (error) {
        console.log(`   ❌ Error validating SQL syntax: ${error.message}\n`);
        return false;
    }
}

function validateWalletSystemIntegrity() {
    console.log('4. Validating Wallet System Integrity...');
    
    try {
        const schemaPath = path.join(__dirname, 'schema.sql');
        const content = fs.readFileSync(schemaPath, 'utf8');
        
        const checks = [
            {
                name: 'Users table has wallet_balance field',
                test: () => content.includes('`wallet_balance` decimal(10,2)')
            },
            {
                name: 'No old credit_balance references',
                test: () => !content.includes('`credit_balance`')
            },
            {
                name: 'Wallet transactions table exists',
                test: () => content.includes('CREATE TABLE `wallet_transactions`')
            },
            {
                name: 'Proper transaction types defined',
                test: () => content.includes("'top_up','payment','withdrawal','refund','bonus','adjustment'")
            },
            {
                name: 'MYR currency precision (10,2)',
                test: () => content.includes('decimal(10,2)')
            },
            {
                name: 'Billplz integration fields present',
                test: () => content.includes('payment_reference') && content.includes('payment_status')
            }
        ];
        
        let allPassed = true;
        checks.forEach((check, index) => {
            const result = check.test();
            console.log(`   ${index + 1}. ${check.name}: ${result ? '✅ PASS' : '❌ FAIL'}`);
            if (!result) allPassed = false;
        });
        
        if (allPassed) {
            console.log('   🎉 Wallet system integrity: PASS\n');
            return true;
        } else {
            console.log('   ❌ Wallet system integrity: FAIL\n');
            return false;
        }
        
    } catch (error) {
        console.log(`   ❌ Error validating wallet system: ${error.message}\n`);
        return false;
    }
}

// Run all validations
const test1 = validateTableOrder();
const test2 = validateForeignKeyReferences();
const test3 = validateSQLSyntax();
const test4 = validateWalletSystemIntegrity();

const allTestsPassed = test1 && test2 && test3 && test4;

// Summary
console.log('📊 Validation Summary');
console.log('=====================');
console.log(`Total Validations: 4`);
console.log(`Passed: ${[test1, test2, test3, test4].filter(Boolean).length}`);
console.log(`Failed: ${[test1, test2, test3, test4].filter(t => !t).length}`);
console.log(`Success Rate: ${Math.round(([test1, test2, test3, test4].filter(Boolean).length / 4) * 100)}%`);

if (allTestsPassed) {
    console.log('\n🎉 All validations passed! Schema is ready for import.');
    console.log('\n✅ Import Command:');
    console.log('   mysql -u username -p database_name < schema.sql');
    console.log('\n✅ The schema should import without foreign key errors.');
} else {
    console.log('\n❌ Some validations failed. Please fix the issues before importing.');
}
