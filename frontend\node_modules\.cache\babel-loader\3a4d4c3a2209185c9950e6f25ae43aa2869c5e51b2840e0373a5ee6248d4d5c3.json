{"ast": null, "code": "import React,{Component}from'react';import{<PERSON><PERSON>,Box,Button,Typography}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";class ErrorBoundary extends Component{constructor(){super(...arguments);this.state={hasError:false};this.handleReset=()=>{this.setState({hasError:false,error:undefined});};}static getDerivedStateFromError(error){return{hasError:true,error};}componentDidCatch(error,errorInfo){console.error('ErrorBoundary caught an error:',error,errorInfo);}render(){if(this.state.hasError){var _this$state$error;if(this.props.fallback){return this.props.fallback;}return/*#__PURE__*/_jsx(Box,{sx:{p:3},children:/*#__PURE__*/_jsxs(<PERSON><PERSON>,{severity:\"error\",sx:{mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Something went wrong\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mb:2},children:((_this$state$error=this.state.error)===null||_this$state$error===void 0?void 0:_this$state$error.message)||'An unexpected error occurred'}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",onClick:this.handleReset,children:\"Try Again\"})]})});}return this.props.children;}}export default ErrorBoundary;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "Box", "<PERSON><PERSON>", "Typography", "jsx", "_jsx", "jsxs", "_jsxs", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "arguments", "state", "<PERSON><PERSON><PERSON><PERSON>", "handleReset", "setState", "error", "undefined", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "console", "render", "_this$state$error", "props", "fallback", "sx", "p", "children", "severity", "mb", "variant", "gutterBottom", "message", "onClick"], "sources": ["C:/laragon/www/frontend/src/components/common/ErrorBoundary.tsx"], "sourcesContent": ["import React, { Component, ErrorInfo, ReactNode } from 'react';\nimport { Alert, Box, Button, Typography } from '@mui/material';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  public state: State = {\n    hasError: false\n  };\n\n  public static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error };\n  }\n\n  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n  }\n\n  private handleReset = () => {\n    this.setState({ hasError: false, error: undefined });\n  };\n\n  public render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      return (\n        <Box sx={{ p: 3 }}>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Something went wrong\n            </Typography>\n            <Typography variant=\"body2\" sx={{ mb: 2 }}>\n              {this.state.error?.message || 'An unexpected error occurred'}\n            </Typography>\n            <Button variant=\"outlined\" onClick={this.handleReset}>\n              Try Again\n            </Button>\n          </Alert>\n        </Box>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAA8B,OAAO,CAC9D,OAASC,KAAK,CAAEC,GAAG,CAAEC,MAAM,CAAEC,UAAU,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAY/D,KAAM,CAAAC,aAAa,QAAS,CAAAT,SAAwB,CAAAU,YAAA,WAAAC,SAAA,OAC3CC,KAAK,CAAU,CACpBC,QAAQ,CAAE,KACZ,CAAC,MAUOC,WAAW,CAAG,IAAM,CAC1B,IAAI,CAACC,QAAQ,CAAC,CAAEF,QAAQ,CAAE,KAAK,CAAEG,KAAK,CAAEC,SAAU,CAAC,CAAC,CACtD,CAAC,EAVD,MAAc,CAAAC,wBAAwBA,CAACF,KAAY,CAAS,CAC1D,MAAO,CAAEH,QAAQ,CAAE,IAAI,CAAEG,KAAM,CAAC,CAClC,CAEOG,iBAAiBA,CAACH,KAAY,CAAEI,SAAoB,CAAE,CAC3DC,OAAO,CAACL,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAEI,SAAS,CAAC,CACnE,CAMOE,MAAMA,CAAA,CAAG,CACd,GAAI,IAAI,CAACV,KAAK,CAACC,QAAQ,CAAE,KAAAU,iBAAA,CACvB,GAAI,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAE,CACvB,MAAO,KAAI,CAACD,KAAK,CAACC,QAAQ,CAC5B,CAEA,mBACEnB,IAAA,CAACJ,GAAG,EAACwB,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAC,QAAA,cAChBpB,KAAA,CAACP,KAAK,EAAC4B,QAAQ,CAAC,OAAO,CAACH,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAE,CAAE,CAAAF,QAAA,eACpCtB,IAAA,CAACF,UAAU,EAAC2B,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAJ,QAAA,CAAC,sBAEtC,CAAY,CAAC,cACbtB,IAAA,CAACF,UAAU,EAAC2B,OAAO,CAAC,OAAO,CAACL,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAE,CAAE,CAAAF,QAAA,CACvC,EAAAL,iBAAA,KAAI,CAACX,KAAK,CAACI,KAAK,UAAAO,iBAAA,iBAAhBA,iBAAA,CAAkBU,OAAO,GAAI,8BAA8B,CAClD,CAAC,cACb3B,IAAA,CAACH,MAAM,EAAC4B,OAAO,CAAC,UAAU,CAACG,OAAO,CAAE,IAAI,CAACpB,WAAY,CAAAc,QAAA,CAAC,WAEtD,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,CAEV,CAEA,MAAO,KAAI,CAACJ,KAAK,CAACI,QAAQ,CAC5B,CACF,CAEA,cAAe,CAAAnB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}