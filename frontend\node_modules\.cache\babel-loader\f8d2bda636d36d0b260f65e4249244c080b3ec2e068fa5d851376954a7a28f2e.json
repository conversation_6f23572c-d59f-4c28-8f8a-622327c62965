{"ast": null, "code": "import api from'./api';class CreditService{// Get user's credit balance\nasync getBalance(){const response=await api.get('/credit/balance');return response.data;}// Get available credit packages\nasync getPackages(){const response=await api.get('/credit/packages');return response.data.packages;}// Get user's credit transaction history\nasync getTransactions(){let page=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1;const response=await api.get(`/credit/transactions?page=${page}`);return response.data;}// Get credit statistics\nasync getStatistics(){const response=await api.get('/credit/statistics');return response.data;}// Create payment for credit package\nasync createPayment(packageId,redirectUrl){const response=await api.post('/payment/create',{package_id:packageId,redirect_url:redirectUrl});return response.data;}// Check payment status\nasync checkPaymentStatus(transactionId){const response=await api.get('/payment/status',{params:{transaction_id:transactionId}});return response.data;}// Get payment configuration\nasync getPaymentConfig(){const response=await api.get('/payment/config');return response.data;}// Format credit amount for display\nformatCredits(amount){try{// Handle falsy values\nif(!amount&&amount!==0){return'0 credits';}// Ensure we have a valid number\nlet numericAmount;if(typeof amount==='number'){numericAmount=amount;}else{numericAmount=parseFloat(String(amount));}// Validate the number\nif(isNaN(numericAmount)||!isFinite(numericAmount)){return'0 credits';}// Ensure non-negative and integer for credits\nconst safeAmount=Math.max(0,Math.floor(numericAmount));return`${safeAmount.toLocaleString()} credits`;}catch(error){console.error('formatCredits error:',error,'amount:',amount);return'0 credits';}}// Format currency for display\nformatCurrency(amount){try{// Handle falsy values\nif(!amount&&amount!==0){return'RM 0.00';}// Ensure we have a valid number\nlet numericAmount;if(typeof amount==='number'){numericAmount=amount;}else{numericAmount=parseFloat(String(amount));}// Validate the number\nif(isNaN(numericAmount)||!isFinite(numericAmount)){return'RM 0.00';}// Ensure non-negative for currency\nconst safeAmount=Math.max(0,numericAmount);return`RM ${safeAmount.toFixed(2)}`;}catch(error){console.error('formatCurrency error:',error,'amount:',amount);return'RM 0.00';}}// Get transaction type color\ngetTransactionTypeColor(type){switch(type){case'purchase':return'success';case'usage':return'warning';case'refund':return'error';case'bonus':return'info';default:return'default';}}// Get payment status color\ngetPaymentStatusColor(status){switch(status){case'completed':return'success';case'pending':return'warning';case'failed':return'error';default:return'default';}}}export default new CreditService();", "map": {"version": 3, "names": ["api", "CreditService", "getBalance", "response", "get", "data", "getPackages", "packages", "getTransactions", "page", "arguments", "length", "undefined", "getStatistics", "createPayment", "packageId", "redirectUrl", "post", "package_id", "redirect_url", "checkPaymentStatus", "transactionId", "params", "transaction_id", "getPaymentConfig", "formatCredits", "amount", "numericAmount", "parseFloat", "String", "isNaN", "isFinite", "safeAmount", "Math", "max", "floor", "toLocaleString", "error", "console", "formatCurrency", "toFixed", "getTransactionTypeColor", "type", "getPaymentStatusColor", "status"], "sources": ["C:/laragon/www/frontend/src/services/creditService.ts"], "sourcesContent": ["import api, { endpoints } from './api';\n\nexport interface CreditPackage {\n  id: number;\n  name: string;\n  description: string;\n  price: number;\n  formatted_price: string;\n  credit_amount: number;\n  price_per_credit: number;\n  features: string[];\n}\n\nexport interface CreditTransaction {\n  id: number;\n  type: string;\n  credit_amount: number;\n  amount_paid: number | null;\n  formatted_amount_paid: string | null;\n  payment_method: string | null;\n  payment_status: string;\n  description: string;\n  package_name: string | null;\n  is_credit: boolean;\n  is_debit: boolean;\n  processed_at: string | null;\n  created_at: string;\n}\n\nexport interface CreditBalance {\n  credit_balance: number;\n  user_id: number;\n}\n\nexport interface CreditStatistics {\n  current_balance: number;\n  total_purchased: number;\n  total_used: number;\n  total_spent: number;\n  recent_transactions: Array<{\n    id: number;\n    type: string;\n    credit_amount: number;\n    description: string;\n    created_at: string;\n  }>;\n}\n\nexport interface PaymentResponse {\n  success: boolean;\n  payment_url?: string;\n  bill_id?: string;\n  transaction_id?: number;\n  error?: string;\n}\n\nexport interface PaymentStatus {\n  transaction_id: number;\n  payment_status: string;\n  credit_amount: number;\n  amount_paid: number;\n  processed_at: string | null;\n}\n\nexport interface PaymentConfig {\n  billplz_enabled: boolean;\n  billplz_configured: boolean;\n}\n\nclass CreditService {\n  // Get user's credit balance\n  async getBalance(): Promise<CreditBalance> {\n    const response = await api.get('/credit/balance');\n    return response.data;\n  }\n\n  // Get available credit packages\n  async getPackages(): Promise<CreditPackage[]> {\n    const response = await api.get('/credit/packages');\n    return response.data.packages;\n  }\n\n  // Get user's credit transaction history\n  async getTransactions(page: number = 1): Promise<{\n    transactions: {\n      data: CreditTransaction[];\n      current_page: number;\n      last_page: number;\n      per_page: number;\n      total: number;\n    };\n  }> {\n    const response = await api.get(`/credit/transactions?page=${page}`);\n    return response.data;\n  }\n\n  // Get credit statistics\n  async getStatistics(): Promise<CreditStatistics> {\n    const response = await api.get('/credit/statistics');\n    return response.data;\n  }\n\n  // Create payment for credit package\n  async createPayment(packageId: number, redirectUrl?: string): Promise<PaymentResponse> {\n    const response = await api.post('/payment/create', {\n      package_id: packageId,\n      redirect_url: redirectUrl,\n    });\n    return response.data;\n  }\n\n  // Check payment status\n  async checkPaymentStatus(transactionId: number): Promise<PaymentStatus> {\n    const response = await api.get('/payment/status', {\n      params: { transaction_id: transactionId },\n    });\n    return response.data;\n  }\n\n  // Get payment configuration\n  async getPaymentConfig(): Promise<PaymentConfig> {\n    const response = await api.get('/payment/config');\n    return response.data;\n  }\n\n  // Format credit amount for display\n  formatCredits(amount: any): string {\n    try {\n      // Handle falsy values\n      if (!amount && amount !== 0) {\n        return '0 credits';\n      }\n\n      // Ensure we have a valid number\n      let numericAmount: number;\n      if (typeof amount === 'number') {\n        numericAmount = amount;\n      } else {\n        numericAmount = parseFloat(String(amount));\n      }\n\n      // Validate the number\n      if (isNaN(numericAmount) || !isFinite(numericAmount)) {\n        return '0 credits';\n      }\n\n      // Ensure non-negative and integer for credits\n      const safeAmount = Math.max(0, Math.floor(numericAmount));\n      return `${safeAmount.toLocaleString()} credits`;\n    } catch (error) {\n      console.error('formatCredits error:', error, 'amount:', amount);\n      return '0 credits';\n    }\n  }\n\n  // Format currency for display\n  formatCurrency(amount: any): string {\n    try {\n      // Handle falsy values\n      if (!amount && amount !== 0) {\n        return 'RM 0.00';\n      }\n\n      // Ensure we have a valid number\n      let numericAmount: number;\n      if (typeof amount === 'number') {\n        numericAmount = amount;\n      } else {\n        numericAmount = parseFloat(String(amount));\n      }\n\n      // Validate the number\n      if (isNaN(numericAmount) || !isFinite(numericAmount)) {\n        return 'RM 0.00';\n      }\n\n      // Ensure non-negative for currency\n      const safeAmount = Math.max(0, numericAmount);\n      return `RM ${safeAmount.toFixed(2)}`;\n    } catch (error) {\n      console.error('formatCurrency error:', error, 'amount:', amount);\n      return 'RM 0.00';\n    }\n  }\n\n  // Get transaction type color\n  getTransactionTypeColor(type: string): 'success' | 'warning' | 'error' | 'info' | 'default' {\n    switch (type) {\n      case 'purchase':\n        return 'success';\n      case 'usage':\n        return 'warning';\n      case 'refund':\n        return 'error';\n      case 'bonus':\n        return 'info';\n      default:\n        return 'default';\n    }\n  }\n\n  // Get payment status color\n  getPaymentStatusColor(status: string): 'success' | 'warning' | 'error' | 'default' {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'failed':\n        return 'error';\n      default:\n        return 'default';\n    }\n  }\n}\n\nexport default new CreditService();\n"], "mappings": "AAAA,MAAO,CAAAA,GAAG,KAAqB,OAAO,CAqEtC,KAAM,CAAAC,aAAc,CAClB;AACA,KAAM,CAAAC,UAAUA,CAAA,CAA2B,CACzC,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAH,GAAG,CAACI,GAAG,CAAC,iBAAiB,CAAC,CACjD,MAAO,CAAAD,QAAQ,CAACE,IAAI,CACtB,CAEA;AACA,KAAM,CAAAC,WAAWA,CAAA,CAA6B,CAC5C,KAAM,CAAAH,QAAQ,CAAG,KAAM,CAAAH,GAAG,CAACI,GAAG,CAAC,kBAAkB,CAAC,CAClD,MAAO,CAAAD,QAAQ,CAACE,IAAI,CAACE,QAAQ,CAC/B,CAEA;AACA,KAAM,CAAAC,eAAeA,CAAA,CAQlB,IARmB,CAAAC,IAAY,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CASpC,KAAM,CAAAP,QAAQ,CAAG,KAAM,CAAAH,GAAG,CAACI,GAAG,CAAC,6BAA6BK,IAAI,EAAE,CAAC,CACnE,MAAO,CAAAN,QAAQ,CAACE,IAAI,CACtB,CAEA;AACA,KAAM,CAAAQ,aAAaA,CAAA,CAA8B,CAC/C,KAAM,CAAAV,QAAQ,CAAG,KAAM,CAAAH,GAAG,CAACI,GAAG,CAAC,oBAAoB,CAAC,CACpD,MAAO,CAAAD,QAAQ,CAACE,IAAI,CACtB,CAEA;AACA,KAAM,CAAAS,aAAaA,CAACC,SAAiB,CAAEC,WAAoB,CAA4B,CACrF,KAAM,CAAAb,QAAQ,CAAG,KAAM,CAAAH,GAAG,CAACiB,IAAI,CAAC,iBAAiB,CAAE,CACjDC,UAAU,CAAEH,SAAS,CACrBI,YAAY,CAAEH,WAChB,CAAC,CAAC,CACF,MAAO,CAAAb,QAAQ,CAACE,IAAI,CACtB,CAEA;AACA,KAAM,CAAAe,kBAAkBA,CAACC,aAAqB,CAA0B,CACtE,KAAM,CAAAlB,QAAQ,CAAG,KAAM,CAAAH,GAAG,CAACI,GAAG,CAAC,iBAAiB,CAAE,CAChDkB,MAAM,CAAE,CAAEC,cAAc,CAAEF,aAAc,CAC1C,CAAC,CAAC,CACF,MAAO,CAAAlB,QAAQ,CAACE,IAAI,CACtB,CAEA;AACA,KAAM,CAAAmB,gBAAgBA,CAAA,CAA2B,CAC/C,KAAM,CAAArB,QAAQ,CAAG,KAAM,CAAAH,GAAG,CAACI,GAAG,CAAC,iBAAiB,CAAC,CACjD,MAAO,CAAAD,QAAQ,CAACE,IAAI,CACtB,CAEA;AACAoB,aAAaA,CAACC,MAAW,CAAU,CACjC,GAAI,CACF;AACA,GAAI,CAACA,MAAM,EAAIA,MAAM,GAAK,CAAC,CAAE,CAC3B,MAAO,WAAW,CACpB,CAEA;AACA,GAAI,CAAAC,aAAqB,CACzB,GAAI,MAAO,CAAAD,MAAM,GAAK,QAAQ,CAAE,CAC9BC,aAAa,CAAGD,MAAM,CACxB,CAAC,IAAM,CACLC,aAAa,CAAGC,UAAU,CAACC,MAAM,CAACH,MAAM,CAAC,CAAC,CAC5C,CAEA;AACA,GAAII,KAAK,CAACH,aAAa,CAAC,EAAI,CAACI,QAAQ,CAACJ,aAAa,CAAC,CAAE,CACpD,MAAO,WAAW,CACpB,CAEA;AACA,KAAM,CAAAK,UAAU,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACE,KAAK,CAACR,aAAa,CAAC,CAAC,CACzD,MAAO,GAAGK,UAAU,CAACI,cAAc,CAAC,CAAC,UAAU,CACjD,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAE,SAAS,CAAEX,MAAM,CAAC,CAC/D,MAAO,WAAW,CACpB,CACF,CAEA;AACAa,cAAcA,CAACb,MAAW,CAAU,CAClC,GAAI,CACF;AACA,GAAI,CAACA,MAAM,EAAIA,MAAM,GAAK,CAAC,CAAE,CAC3B,MAAO,SAAS,CAClB,CAEA;AACA,GAAI,CAAAC,aAAqB,CACzB,GAAI,MAAO,CAAAD,MAAM,GAAK,QAAQ,CAAE,CAC9BC,aAAa,CAAGD,MAAM,CACxB,CAAC,IAAM,CACLC,aAAa,CAAGC,UAAU,CAACC,MAAM,CAACH,MAAM,CAAC,CAAC,CAC5C,CAEA;AACA,GAAII,KAAK,CAACH,aAAa,CAAC,EAAI,CAACI,QAAQ,CAACJ,aAAa,CAAC,CAAE,CACpD,MAAO,SAAS,CAClB,CAEA;AACA,KAAM,CAAAK,UAAU,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEP,aAAa,CAAC,CAC7C,MAAO,MAAMK,UAAU,CAACQ,OAAO,CAAC,CAAC,CAAC,EAAE,CACtC,CAAE,MAAOH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAE,SAAS,CAAEX,MAAM,CAAC,CAChE,MAAO,SAAS,CAClB,CACF,CAEA;AACAe,uBAAuBA,CAACC,IAAY,CAAwD,CAC1F,OAAQA,IAAI,EACV,IAAK,UAAU,CACb,MAAO,SAAS,CAClB,IAAK,OAAO,CACV,MAAO,SAAS,CAClB,IAAK,QAAQ,CACX,MAAO,OAAO,CAChB,IAAK,OAAO,CACV,MAAO,MAAM,CACf,QACE,MAAO,SAAS,CACpB,CACF,CAEA;AACAC,qBAAqBA,CAACC,MAAc,CAA+C,CACjF,OAAQA,MAAM,EACZ,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,SAAS,CACZ,MAAO,SAAS,CAClB,IAAK,QAAQ,CACX,MAAO,OAAO,CAChB,QACE,MAAO,SAAS,CACpB,CACF,CACF,CAEA,cAAe,IAAI,CAAA3C,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}