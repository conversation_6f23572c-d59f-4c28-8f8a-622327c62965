-- =====================================================
-- Admin Account Setup for Wallet-Based Application
-- =====================================================
-- This file contains different methods to create admin accounts
-- Choose the method that best fits your needs
-- =====================================================

-- =====================================================
-- METHOD 1: Direct SQL INSERT (Recommended for initial setup)
-- =====================================================

-- Insert admin user with hashed password
-- Password: 'admin123' (hashed using <PERSON><PERSON>'s bcrypt)
-- Note: You should change this password after first login
INSERT INTO `users` (
    `id`,
    `name`,
    `email`,
    `email_verified_at`,
    `password`,
    `phone`,
    `bio`,
    `avatar`,
    `date_of_birth`,
    `role`,
    `is_active`,
    `wallet_balance`,
    `remember_token`,
    `created_at`,
    `updated_at`
) VALUES (
    1,
    'System Administrator',
    '<EMAIL>',
    NOW(),
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- bcrypt hash of 'password'
    '+***********',
    'System Administrator Account',
    NULL,
    '1990-01-01',
    'admin',
    1,
    1000.00, -- Initial wallet balance of RM 1000.00
    NULL,
    NOW(),
    NOW()
);

-- =====================================================
-- METHOD 2: Multiple Admin Accounts
-- =====================================================

-- Super Admin (Full Access)
INSERT INTO `users` (
    `name`,
    `email`,
    `email_verified_at`,
    `password`,
    `role`,
    `is_active`,
    `wallet_balance`,
    `created_at`,
    `updated_at`
) VALUES (
    'Super Admin',
    '<EMAIL>',
    NOW(),
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'admin',
    1,
    0.00,
    NOW(),
    NOW()
);

-- Customer Support Admin
INSERT INTO `users` (
    `name`,
    `email`,
    `email_verified_at`,
    `password`,
    `role`,
    `is_active`,
    `wallet_balance`,
    `created_at`,
    `updated_at`
) VALUES (
    'Customer Support',
    '<EMAIL>',
    NOW(),
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'admin',
    1,
    0.00,
    NOW(),
    NOW()
);

-- Finance Admin
INSERT INTO `users` (
    `name`,
    `email`,
    `email_verified_at`,
    `password`,
    `role`,
    `is_active`,
    `wallet_balance`,
    `created_at`,
    `updated_at`
) VALUES (
    'Finance Manager',
    '<EMAIL>',
    NOW(),
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'admin',
    1,
    0.00,
    NOW(),
    NOW()
);

-- =====================================================
-- METHOD 3: Admin with Sample Data
-- =====================================================

-- Admin user with complete profile
INSERT INTO `users` (
    `name`,
    `email`,
    `email_verified_at`,
    `password`,
    `phone`,
    `bio`,
    `date_of_birth`,
    `role`,
    `is_active`,
    `wallet_balance`,
    `created_at`,
    `updated_at`
) VALUES (
    'John Admin',
    '<EMAIL>',
    NOW(),
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    '+***********',
    'Senior System Administrator with 5+ years experience in web application management.',
    '1985-06-15',
    'admin',
    1,
    500.00,
    NOW(),
    NOW()
);

-- =====================================================
-- METHOD 4: Test Admin Accounts (Development Only)
-- =====================================================

-- Development Admin (DO NOT USE IN PRODUCTION)
INSERT INTO `users` (
    `name`,
    `email`,
    `email_verified_at`,
    `password`,
    `role`,
    `is_active`,
    `wallet_balance`,
    `created_at`,
    `updated_at`
) VALUES (
    'Dev Admin',
    'dev@localhost',
    NOW(),
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: 'password'
    'admin',
    1,
    9999.99,
    NOW(),
    NOW()
);

-- Test User (Regular User for Testing)
INSERT INTO `users` (
    `name`,
    `email`,
    `email_verified_at`,
    `password`,
    `role`,
    `is_active`,
    `wallet_balance`,
    `created_at`,
    `updated_at`
) VALUES (
    'Test User',
    'test@localhost',
    NOW(),
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: 'password'
    'user',
    1,
    100.00,
    NOW(),
    NOW()
);

-- =====================================================
-- SAMPLE WALLET TRANSACTIONS FOR ADMIN TESTING
-- =====================================================

-- Add some sample wallet transactions for testing
INSERT INTO `wallet_transactions` (
    `user_id`,
    `type`,
    `amount`,
    `payment_status`,
    `description`,
    `processed_at`,
    `created_at`,
    `updated_at`
) VALUES 
-- Initial balance for main admin
(1, 'bonus', 1000.00, 'completed', 'Initial admin wallet balance', NOW(), NOW(), NOW()),

-- Sample top-up transaction
(1, 'top_up', 500.00, 'completed', 'Admin wallet top-up for testing', NOW(), NOW(), NOW()),

-- Sample payment transaction
(1, 'payment', -50.00, 'completed', 'Test payment transaction', NOW(), NOW(), NOW());

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if admin accounts were created successfully
-- SELECT id, name, email, role, is_active, wallet_balance FROM users WHERE role = 'admin';

-- Check wallet transactions
-- SELECT * FROM wallet_transactions WHERE user_id IN (SELECT id FROM users WHERE role = 'admin');

-- Count total users by role
-- SELECT role, COUNT(*) as count FROM users GROUP BY role;
