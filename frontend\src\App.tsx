import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Container } from 'react-bootstrap';
import { AuthProvider } from './contexts/AuthContext';
import Navbar from './components/layout/Navbar';
import Home from './pages/Home';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import ForgotPassword from './pages/auth/ForgotPassword';
import ResetPassword from './pages/auth/ResetPassword';
import Profile from './pages/user/Profile';
import EditProfile from './pages/user/EditProfile';
import PageView from './pages/cms/PageView';
import ProtectedRoute from './components/auth/ProtectedRoute';
import EmailVerificationNotice from './components/auth/EmailVerificationNotice';
// Material Dashboard imports
import DashboardRoute from './components/dashboard/DashboardRoute';
import Dashboard from './pages/dashboard/Dashboard';
import Wallet from './pages/dashboard/Wallet';
import Order from './pages/dashboard/Order';
import Orders from './pages/dashboard/Orders';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Dashboard routes - these don't use the main layout */}
          <Route
            path="/dashboard"
            element={
              <DashboardRoute>
                <Dashboard />
              </DashboardRoute>
            }
          />
          <Route
            path="/dashboard/wallet"
            element={
              <DashboardRoute>
                <Wallet />
              </DashboardRoute>
            }
          />
          {/* Redirect old credit route to new wallet route */}
          <Route
            path="/dashboard/credit"
            element={<Navigate to="/dashboard/wallet" replace />}
          />
          <Route
            path="/dashboard/order"
            element={
              <DashboardRoute>
                <Order />
              </DashboardRoute>
            }
          />
          <Route
            path="/dashboard/orders"
            element={
              <DashboardRoute>
                <Orders />
              </DashboardRoute>
            }
          />


          {/* Main application routes with Bootstrap layout */}
          <Route
            path="/*"
            element={
              <div className="App">
                <Navbar />
                <Container className="mt-4">
                  <Routes>
                    {/* Public routes */}
                    <Route path="/" element={<Home />} />
                    <Route path="/login" element={<Login />} />
                    <Route path="/register" element={<Register />} />
                    <Route path="/forgot-password" element={<ForgotPassword />} />
                    <Route path="/reset-password" element={<ResetPassword />} />
                    <Route path="/pages/:slug" element={<PageView />} />

                    {/* Protected routes */}
                    <Route
                      path="/profile"
                      element={
                        <ProtectedRoute>
                          <Profile />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/profile/edit"
                      element={
                        <ProtectedRoute>
                          <EditProfile />
                        </ProtectedRoute>
                      }
                    />

                    {/* Email verification notice */}
                    <Route
                      path="/email-verification"
                      element={
                        <ProtectedRoute>
                          <EmailVerificationNotice />
                        </ProtectedRoute>
                      }
                    />

                    {/* Catch all route */}
                    <Route path="*" element={<Navigate to="/" replace />} />
                  </Routes>
                </Container>
              </div>
            }
          />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
