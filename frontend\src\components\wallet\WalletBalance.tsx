import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Skeleton,
  Alert,
  IconButton,
  Tooltip,
  Chip,
  Grid,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  AccountBalanceWallet,
  Refresh,
  TrendingUp,
  TrendingDown,
  History,
  Add,
} from '@mui/icons-material';
import creditService, { CreditStatistics } from '../../services/creditService';

interface WalletBalanceProps {
  refreshTrigger?: number;
  onTopUpClick?: () => void;
  onHistoryClick?: () => void;
}

const WalletBalance: React.FC<WalletBalanceProps> = ({
  refreshTrigger = 0,
  onTopUpClick,
  onHistoryClick,
}) => {
  const [statistics, setStatistics] = useState<CreditStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const fetchStatistics = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      setError(null);
      const data = await creditService.getStatistics();
      setStatistics(data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to load wallet balance');
    } finally {
      setLoading(false);
      if (showRefreshing) setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, [refreshTrigger]);

  const handleRefresh = () => {
    fetchStatistics(true);
  };

  if (loading) {
    return (
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Skeleton variant="circular" width={48} height={48} />
            <Box flex={1}>
              <Skeleton variant="text" width="60%" height={32} />
              <Skeleton variant="text" width="40%" height={20} />
            </Box>
          </Box>
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', sm: 'repeat(3, 1fr)' },
              gap: 2
            }}
          >
            <Skeleton variant="rectangular" height={80} />
            <Skeleton variant="rectangular" height={80} />
            <Skeleton variant="rectangular" height={80} />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  if (!statistics) {
    return null;
  }

  const currentBalance = statistics.current_balance || 0;
  const totalSpent = statistics.total_spent || 0;
  const totalPurchased = statistics.total_purchased || 0;

  return (
    <Card 
      sx={{ 
        mb: 3,
        background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          right: 0,
          width: '200px',
          height: '200px',
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '50%',
          transform: 'translate(50%, -50%)',
        }
      }}
    >
      <CardContent sx={{ position: 'relative', zIndex: 1 }}>
        {/* Header */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
          <Box display="flex" alignItems="center" gap={2}>
            <AccountBalanceWallet sx={{ fontSize: 40, opacity: 0.9 }} />
            <Box>
              <Typography variant="h6" sx={{ opacity: 0.9, fontWeight: 500 }}>
                Wallet Balance
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.7 }}>
                Malaysian Ringgit (MYR)
              </Typography>
            </Box>
          </Box>
          <Tooltip title="Refresh Balance">
            <IconButton 
              onClick={handleRefresh} 
              disabled={refreshing}
              sx={{ 
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' }
              }}
            >
              <Refresh sx={{ 
                animation: refreshing ? 'spin 1s linear infinite' : 'none',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' },
                }
              }} />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Main Balance */}
        <Box textAlign="center" mb={4}>
          <Typography 
            variant="h2" 
            component="div" 
            sx={{ 
              fontWeight: 300,
              fontSize: isMobile ? '2.5rem' : '3.5rem',
              lineHeight: 1,
              mb: 1
            }}
          >
            {creditService.formatWalletBalance(currentBalance)}
          </Typography>
          <Chip 
            label={currentBalance > 0 ? "Active" : "Empty"} 
            color={currentBalance > 0 ? "success" : "warning"}
            size="small"
            sx={{ 
              backgroundColor: currentBalance > 0 ? 'rgba(76, 175, 80, 0.2)' : 'rgba(255, 152, 0, 0.2)',
              color: 'white',
              border: `1px solid ${currentBalance > 0 ? 'rgba(76, 175, 80, 0.5)' : 'rgba(255, 152, 0, 0.5)'}`
            }}
          />
        </Box>

        {/* Statistics Grid */}
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: 'repeat(3, 1fr)' },
            gap: 2
          }}
        >
          <Box
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: 2,
              p: 2,
              textAlign: 'center',
              backdropFilter: 'blur(10px)',
            }}
          >
            <TrendingUp sx={{ fontSize: 24, mb: 1, opacity: 0.8 }} />
            <Typography variant="body2" sx={{ opacity: 0.8, mb: 0.5 }}>
              Total Added
            </Typography>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {creditService.formatWalletBalance(totalPurchased)}
            </Typography>
          </Box>

          <Box
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: 2,
              p: 2,
              textAlign: 'center',
              backdropFilter: 'blur(10px)',
            }}
          >
            <TrendingDown sx={{ fontSize: 24, mb: 1, opacity: 0.8 }} />
            <Typography variant="body2" sx={{ opacity: 0.8, mb: 0.5 }}>
              Total Spent
            </Typography>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {creditService.formatWalletBalance(totalSpent)}
            </Typography>
          </Box>

          <Box
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: 2,
              p: 2,
              textAlign: 'center',
              backdropFilter: 'blur(10px)',
            }}
          >
            <History sx={{ fontSize: 24, mb: 1, opacity: 0.8 }} />
            <Typography variant="body2" sx={{ opacity: 0.8, mb: 0.5 }}>
              Recent Activity
            </Typography>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {statistics.recent_transactions?.length || 0} items
            </Typography>
          </Box>
        </Box>

        {/* Quick Actions */}
        <Box display="flex" gap={2} mt={3} justifyContent="center">
          <Tooltip title="Top Up Wallet">
            <IconButton 
              onClick={onTopUpClick}
              sx={{ 
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.3)' },
                backdropFilter: 'blur(10px)',
              }}
            >
              <Add />
            </IconButton>
          </Tooltip>
          <Tooltip title="View History">
            <IconButton 
              onClick={onHistoryClick}
              sx={{ 
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.3)' },
                backdropFilter: 'blur(10px)',
              }}
            >
              <History />
            </IconButton>
          </Tooltip>
        </Box>
      </CardContent>
    </Card>
  );
};

export default WalletBalance;
